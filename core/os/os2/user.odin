package os2

import "base:runtime"

// ```
// Windows:  C:\Users\<USER>\Users\Alice\AppData\Local
// macOS:    /Users/<USER>/Library/Caches
// Linux:    /home/<USER>/.cache
// ```
@(require_results)
user_cache_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_cache_dir(allocator)
}

// User-hidden application data
//
// ```
// Windows:  C:\Users\<USER>\AppData\Local ("C:\Users\<USER>\AppData\Roaming" if `roaming`)
// macOS:    /Users/<USER>/Library/Application Support
// Linux:    /home/<USER>/.local/share
// ```
//
// NOTE: (Windows only) `roaming` is for syncing across multiple devices within a *domain network*
@(require_results)
user_data_dir :: proc(allocator: runtime.Allocator, roaming := false) -> (dir: string, err: Error) {
	return _user_data_dir(allocator, roaming)
}

// Non-essential application data, e.g. history, ui layout state
//
// ```
// Windows:  C:\Users\<USER>\AppData\Local
// macOS:    /Users/<USER>/Library/Application Support
// Linux:    /home/<USER>/.local/state
// ```
@(require_results)
user_state_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_state_dir(allocator)
}

// Application log files
//
// ```
// Windows:  C:\Users\<USER>\AppData\Local
// macOS:    /Users/<USER>/Library/Logs
// Linux:    /home/<USER>/.local/state
// ```
@(require_results)
user_log_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_log_dir(allocator)
}

// Application settings/preferences
//
// ```
// Windows:  C:\Users\<USER>\AppData\Local ("C:\Users\<USER>\AppData\Roaming" if `roaming`)
// macOS:    /Users/<USER>/Library/Application Support
// Linux:    /home/<USER>/.config
// ```
//
// NOTE: (Windows only) `roaming` is for syncing across multiple devices within a *domain network*
@(require_results)
user_config_dir :: proc(allocator: runtime.Allocator, roaming := false) -> (dir: string, err: Error) {
	return _user_config_dir(allocator, roaming)
}

// ```
// Windows:  C:\Users\<USER>\Music
// macOS:    /Users/<USER>/Music
// Linux:    /home/<USER>/Music
// ```
@(require_results)
user_music_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_music_dir(allocator)
}

// ```
// Windows:  C:\Users\<USER>\Desktop
// macOS:    /Users/<USER>/Desktop
// Linux:    /home/<USER>/Desktop
// ```
@(require_results)
user_desktop_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_desktop_dir(allocator)
}

// ```
// Windows:  C:\Users\<USER>\Documents
// macOS:    /Users/<USER>/Documents
// Linux:    /home/<USER>/Documents
// ```
@(require_results)
user_documents_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_documents_dir(allocator)
}

// ```
// Windows:  C:\Users\<USER>\Downloads
// macOS:    /Users/<USER>/Downloads
// Linux:    /home/<USER>/Downloads
// ```
@(require_results)
user_downloads_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_downloads_dir(allocator)
}

// ```
// Windows:  C:\Users\<USER>\Pictures
// macOS:    /Users/<USER>/Pictures
// Linux:    /home/<USER>/Pictures
// ```
@(require_results)
user_pictures_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_pictures_dir(allocator)
}

// ```
// Windows:  C:\Users\<USER>\Public
// macOS:    /Users/<USER>/Public
// Linux:    /home/<USER>/Public
// ```
@(require_results)
user_public_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_public_dir(allocator)
}

// ```
// Windows:  C:\Users\<USER>\Videos
// macOS:    /Users/<USER>/Movies
// Linux:    /home/<USER>/Videos
// ```
@(require_results)
user_videos_dir :: proc(allocator: runtime.Allocator) -> (dir: string, err: Error) {
	return _user_videos_dir(allocator)
}