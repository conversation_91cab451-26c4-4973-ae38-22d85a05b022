package sysinfo

CPU_Feature :: enum u64 {
	// Bit-Manipulation ISA Extensions v1.
	Zba = 3,
	Zbb,
	Zbs,

	// CMOs (ratified).
	Zicboz,

	// Bit-Manipulation ISA Extensions v1.
	Zbc,

	// Scalar Crypto ISA extensions v1.
	Zbkb,
	Zbkc,
	Zbkx,
	Zknd,
	Zkne,
	Zknh,
	Zksed,
	Zksh,
	Zkt,

	// Cryptography Extensions Volume II v1.
	Zvbb,
	Zvbc,
	Zvkb,
	Zvkg,
	Zvkned,
	Zvknha,
	Zvknhb,
	Zvksed,
	Zvksh,
	Zvkt,

	// ISA Manual v1.
	Zfh,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ntl,

	// ISA manual (ratified).
	<PERSON>v<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,

	// Atomic Compare-and-Swap Instructions Manual (ratified).
	Zacas,
	Zicond,

	// ISA manual (ratified).
	Zihintpause,

	// Vector Extensions Manual v1.
	Zve32x,
	Zve32f,
	<PERSON>ve64x,
	<PERSON>ve64f,
	<PERSON>ve64d,

	// ISA manual (ratified).
	<PERSON><PERSON><PERSON>,

	// Code Size Reduction (ratified).
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,

	// ISA manual (ratified).
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,

 	// Base features, don't think this is ever not here.
	I,
 	// Integer multiplication and division, currently required by Odin.
	M,
 	// Atomics.
	A,
 	// Single precision floating point, currently required by Odin.
	F,
 	// Double precision floating point, currently required by Odin.
	D,
 	// Compressed instructions.
	C,
 	// Vector operations.
	V,

	// Indicates Misaligned Scalar Loads will not trap the program.
	Misaligned_Supported,
	// Indicates Hardware Support for Misaligned Scalar Loads.
	Misaligned_Fast,
}

CPU_Features :: distinct bit_set[CPU_Feature; u64]
CPU :: struct {
	name:           Maybe(string),
	features:       Maybe(CPU_Features),
	physical_cores: int,
	logical_cores:  int,
}
cpu: CPU