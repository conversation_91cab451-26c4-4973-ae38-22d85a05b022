#+build i386
package linux

// The numbers are taken from
//    /arch/x86/entry/syscalls/syscall_32.tbl
// in Linux headers. Only x64 and common ABI
// syscalls were taken, for x32 is not
// supported by Odin

// This syscall is only used by the kernel internally
// userspace has no reason to use it.
SYS_restart_syscall              :: uintptr(0)

SYS_exit                         :: uintptr(1)
SYS_fork                         :: uintptr(2)
SYS_read                         :: uintptr(3)
SYS_write                        :: uintptr(4)
SYS_open                         :: uintptr(5)
SYS_close                        :: uintptr(6)
SYS_waitpid                      :: uintptr(7)
SYS_creat                        :: uintptr(8)
SYS_link                         :: uintptr(9)
SYS_unlink                       :: uintptr(10)
SYS_execve                       :: uintptr(11)
SYS_chdir                        :: uintptr(12)
SYS_time                         :: uintptr(13)
SYS_mknod                        :: uintptr(14)
SYS_chmod                        :: uintptr(15)
SYS_lchown                       :: uintptr(16)
SYS_break                        :: uintptr(17)
SYS_oldstat                      :: uintptr(18)
SYS_lseek                        :: uintptr(19)
SYS_getpid                       :: uintptr(20)
SYS_mount                        :: uintptr(21)
SYS_umount                       :: uintptr(22)
SYS_setuid                       :: uintptr(23)
SYS_getuid                       :: uintptr(24)
SYS_stime                        :: uintptr(25)
SYS_ptrace                       :: uintptr(26)
SYS_alarm                        :: uintptr(27)
SYS_oldfstat                     :: uintptr(28)
SYS_pause                        :: uintptr(29)
SYS_utime                        :: uintptr(30)
SYS_stty                         :: uintptr(31)
SYS_gtty                         :: uintptr(32)
SYS_access                       :: uintptr(33)
SYS_nice                         :: uintptr(34)
SYS_ftime                        :: uintptr(35)
SYS_sync                         :: uintptr(36)
SYS_kill                         :: uintptr(37)
SYS_rename                       :: uintptr(38)
SYS_mkdir                        :: uintptr(39)
SYS_rmdir                        :: uintptr(40)
SYS_dup                          :: uintptr(41)
SYS_pipe                         :: uintptr(42)
SYS_times                        :: uintptr(43)
SYS_prof                         :: uintptr(44)
SYS_brk                          :: uintptr(45)
SYS_setgid                       :: uintptr(46)
SYS_getgid                       :: uintptr(47)
SYS_signal                       :: uintptr(48)
SYS_geteuid                      :: uintptr(49)
SYS_getegid                      :: uintptr(50)
SYS_acct                         :: uintptr(51)
SYS_umount2                      :: uintptr(52)
SYS_lock                         :: uintptr(53)
SYS_ioctl                        :: uintptr(54)
SYS_fcntl                        :: uintptr(55)
SYS_mpx                          :: uintptr(56)
SYS_setpgid                      :: uintptr(57)
SYS_ulimit                       :: uintptr(58)
SYS_oldolduname                  :: uintptr(59)
SYS_umask                        :: uintptr(60)
SYS_chroot                       :: uintptr(61)
SYS_ustat                        :: uintptr(62)
SYS_dup2                         :: uintptr(63)
SYS_getppid                      :: uintptr(64)
SYS_getpgrp                      :: uintptr(65)
SYS_setsid                       :: uintptr(66)
SYS_sigaction                    :: uintptr(67)
SYS_sgetmask                     :: uintptr(68)
SYS_ssetmask                     :: uintptr(69)
SYS_setreuid                     :: uintptr(70)
SYS_setregid                     :: uintptr(71)
SYS_sigsuspend                   :: uintptr(72)
SYS_sigpending                   :: uintptr(73)
SYS_sethostname                  :: uintptr(74)
SYS_setrlimit                    :: uintptr(75)
SYS_getrlimit                    :: uintptr(76)
SYS_getrusage                    :: uintptr(77)
SYS_gettimeofday                 :: uintptr(78)
SYS_settimeofday                 :: uintptr(79)
SYS_getgroups                    :: uintptr(80)
SYS_setgroups                    :: uintptr(81)
SYS_select                       :: uintptr(82)
SYS_symlink                      :: uintptr(83)
SYS_oldlstat                     :: uintptr(84)
SYS_readlink                     :: uintptr(85)
SYS_uselib                       :: uintptr(86)
SYS_swapon                       :: uintptr(87)
SYS_reboot                       :: uintptr(88)
SYS_readdir                      :: uintptr(89)
SYS_mmap                         :: uintptr(90)
SYS_munmap                       :: uintptr(91)
SYS_truncate                     :: uintptr(92)
SYS_ftruncate                    :: uintptr(93)
SYS_fchmod                       :: uintptr(94)
SYS_fchown                       :: uintptr(95)
SYS_getpriority                  :: uintptr(96)
SYS_setpriority                  :: uintptr(97)
SYS_profil                       :: uintptr(98)
SYS_statfs                       :: uintptr(99)
SYS_fstatfs                      :: uintptr(100)
SYS_ioperm                       :: uintptr(101)
SYS_socketcall                   :: uintptr(102)
SYS_syslog                       :: uintptr(103)
SYS_setitimer                    :: uintptr(104)
SYS_getitimer                    :: uintptr(105)
SYS_stat                         :: uintptr(106)
SYS_lstat                        :: uintptr(107)
SYS_fstat                        :: uintptr(108)
SYS_olduname                     :: uintptr(109)
SYS_iopl                         :: uintptr(110)
SYS_vhangup                      :: uintptr(111)
SYS_idle                         :: uintptr(112)
SYS_vm86old                      :: uintptr(113)
SYS_wait4                        :: uintptr(114)
SYS_swapoff                      :: uintptr(115)
SYS_sysinfo                      :: uintptr(116)
SYS_ipc                          :: uintptr(117)
SYS_fsync                        :: uintptr(118)
SYS_sigreturn                    :: uintptr(119)
SYS_clone                        :: uintptr(120)
SYS_setdomainname                :: uintptr(121)
SYS_uname                        :: uintptr(122)
SYS_modify_ldt                   :: uintptr(123)
SYS_adjtimex                     :: uintptr(124)
SYS_mprotect                     :: uintptr(125)
SYS_sigprocmask                  :: uintptr(126)
SYS_create_module                :: uintptr(127)
SYS_init_module                  :: uintptr(128)
SYS_delete_module                :: uintptr(129)
SYS_get_kernel_syms              :: uintptr(130)
SYS_quotactl                     :: uintptr(131)
SYS_getpgid                      :: uintptr(132)
SYS_fchdir                       :: uintptr(133)
SYS_bdflush                      :: uintptr(134)
SYS_sysfs                        :: uintptr(135)
SYS_personality                  :: uintptr(136)
SYS_afs_syscall                  :: uintptr(137)
SYS_setfsuid                     :: uintptr(138)
SYS_setfsgid                     :: uintptr(139)
SYS__llseek                      :: uintptr(140)
SYS_getdents                     :: uintptr(141)
SYS__newselect                   :: uintptr(142)
SYS_flock                        :: uintptr(143)
SYS_msync                        :: uintptr(144)
SYS_readv                        :: uintptr(145)
SYS_writev                       :: uintptr(146)
SYS_getsid                       :: uintptr(147)
SYS_fdatasync                    :: uintptr(148)
SYS__sysctl                      :: uintptr(149)
SYS_mlock                        :: uintptr(150)
SYS_munlock                      :: uintptr(151)
SYS_mlockall                     :: uintptr(152)
SYS_munlockall                   :: uintptr(153)
SYS_sched_setparam               :: uintptr(154)
SYS_sched_getparam               :: uintptr(155)
SYS_sched_setscheduler           :: uintptr(156)
SYS_sched_getscheduler           :: uintptr(157)
SYS_sched_yield                  :: uintptr(158)
SYS_sched_get_priority_max       :: uintptr(159)
SYS_sched_get_priority_min       :: uintptr(160)
SYS_sched_rr_get_interval        :: uintptr(161)
SYS_nanosleep                    :: uintptr(162)
SYS_mremap                       :: uintptr(163)
SYS_setresuid                    :: uintptr(164)
SYS_getresuid                    :: uintptr(165)
SYS_vm86                         :: uintptr(166)
SYS_query_module                 :: uintptr(167)
SYS_poll                         :: uintptr(168)
SYS_nfsservctl                   :: uintptr(169)
SYS_setresgid                    :: uintptr(170)
SYS_getresgid                    :: uintptr(171)
SYS_prctl                        :: uintptr(172)
SYS_rt_sigreturn                 :: uintptr(173)
SYS_rt_sigaction                 :: uintptr(174)
SYS_rt_sigprocmask               :: uintptr(175)
SYS_rt_sigpending                :: uintptr(176)
SYS_rt_sigtimedwait              :: uintptr(177)
SYS_rt_sigqueueinfo              :: uintptr(178)
SYS_rt_sigsuspend                :: uintptr(179)
SYS_pread64                      :: uintptr(180)
SYS_pwrite64                     :: uintptr(181)
SYS_chown                        :: uintptr(182)
SYS_getcwd                       :: uintptr(183)
SYS_capget                       :: uintptr(184)
SYS_capset                       :: uintptr(185)
SYS_sigaltstack                  :: uintptr(186)
SYS_sendfile                     :: uintptr(187)
SYS_getpmsg                      :: uintptr(188)
SYS_putpmsg                      :: uintptr(189)
SYS_vfork                        :: uintptr(190)
SYS_ugetrlimit                   :: uintptr(191)
SYS_mmap2                        :: uintptr(192)
SYS_truncate64                   :: uintptr(193)
SYS_ftruncate64                  :: uintptr(194)
SYS_stat64                       :: uintptr(195)
SYS_lstat64                      :: uintptr(196)
SYS_fstat64                      :: uintptr(197)
SYS_lchown32                     :: uintptr(198)
SYS_getuid32                     :: uintptr(199)
SYS_getgid32                     :: uintptr(200)
SYS_geteuid32                    :: uintptr(201)
SYS_getegid32                    :: uintptr(202)
SYS_setreuid32                   :: uintptr(203)
SYS_setregid32                   :: uintptr(204)
SYS_getgroups32                  :: uintptr(205)
SYS_setgroups32                  :: uintptr(206)
SYS_fchown32                     :: uintptr(207)
SYS_setresuid32                  :: uintptr(208)
SYS_getresuid32                  :: uintptr(209)
SYS_setresgid32                  :: uintptr(210)
SYS_getresgid32                  :: uintptr(211)
SYS_chown32                      :: uintptr(212)
SYS_setuid32                     :: uintptr(213)
SYS_setgid32                     :: uintptr(214)
SYS_setfsuid32                   :: uintptr(215)
SYS_setfsgid32                   :: uintptr(216)
SYS_pivot_root                   :: uintptr(217)
SYS_mincore                      :: uintptr(218)
SYS_madvise                      :: uintptr(219)
SYS_getdents64                   :: uintptr(220)
SYS_fcntl64                      :: uintptr(221)
// 222 is unused
// 223 is unused
SYS_gettid                       :: uintptr(224)
SYS_readahead                    :: uintptr(225)
SYS_setxattr                     :: uintptr(226)
SYS_lsetxattr                    :: uintptr(227)
SYS_fsetxattr                    :: uintptr(228)
SYS_getxattr                     :: uintptr(229)
SYS_lgetxattr                    :: uintptr(230)
SYS_fgetxattr                    :: uintptr(231)
SYS_listxattr                    :: uintptr(232)
SYS_llistxattr                   :: uintptr(233)
SYS_flistxattr                   :: uintptr(234)
SYS_removexattr                  :: uintptr(235)
SYS_lremovexattr                 :: uintptr(236)
SYS_fremovexattr                 :: uintptr(237)
SYS_tkill                        :: uintptr(238)
SYS_sendfile64                   :: uintptr(239)
SYS_futex                        :: uintptr(240)
SYS_sched_setaffinity            :: uintptr(241)
SYS_sched_getaffinity            :: uintptr(242)
SYS_set_thread_area              :: uintptr(243)
SYS_get_thread_area              :: uintptr(244)
SYS_io_setup                     :: uintptr(245)
SYS_io_destroy                   :: uintptr(246)
SYS_io_getevents                 :: uintptr(247)
SYS_io_submit                    :: uintptr(248)
SYS_io_cancel                    :: uintptr(249)
SYS_fadvise64                    :: uintptr(250)
// 251 is available for reuse (was briefly sys_set_zone_reclaim)
SYS_exit_group                   :: uintptr(252)
SYS_lookup_dcookie               :: uintptr(253)
SYS_epoll_create                 :: uintptr(254)
SYS_epoll_ctl                    :: uintptr(255)
SYS_epoll_wait                   :: uintptr(256)
SYS_remap_file_pages             :: uintptr(257)
SYS_set_tid_address              :: uintptr(258)
SYS_timer_create                 :: uintptr(259)
SYS_timer_settime                :: uintptr(260)
SYS_timer_gettime                :: uintptr(261)
SYS_timer_getoverrun             :: uintptr(262)
SYS_timer_delete                 :: uintptr(263)
SYS_clock_settime                :: uintptr(264)
SYS_clock_gettime                :: uintptr(265)
SYS_clock_getres                 :: uintptr(266)
SYS_clock_nanosleep              :: uintptr(267)
SYS_statfs64                     :: uintptr(268)
SYS_fstatfs64                    :: uintptr(269)
SYS_tgkill                       :: uintptr(270)
SYS_utimes                       :: uintptr(271)
SYS_fadvise64_64                 :: uintptr(272)
SYS_vserver                      :: uintptr(273)
SYS_mbind                        :: uintptr(274)
SYS_get_mempolicy                :: uintptr(275)
SYS_set_mempolicy                :: uintptr(276)
SYS_mq_open                      :: uintptr(277)
SYS_mq_unlink                    :: uintptr(278)
SYS_mq_timedsend                 :: uintptr(279)
SYS_mq_timedreceive              :: uintptr(280)
SYS_mq_notify                    :: uintptr(281)
SYS_mq_getsetattr                :: uintptr(282)
SYS_kexec_load                   :: uintptr(283)
SYS_waitid                       :: uintptr(284)
// 285 sys_setaltroot
SYS_add_key                      :: uintptr(286)
SYS_request_key                  :: uintptr(287)
SYS_keyctl                       :: uintptr(288)
SYS_ioprio_set                   :: uintptr(289)
SYS_ioprio_get                   :: uintptr(290)
SYS_inotify_init                 :: uintptr(291)
SYS_inotify_add_watch            :: uintptr(292)
SYS_inotify_rm_watch             :: uintptr(293)
SYS_migrate_pages                :: uintptr(294)
SYS_openat                       :: uintptr(295)
SYS_mkdirat                      :: uintptr(296)
SYS_mknodat                      :: uintptr(297)
SYS_fchownat                     :: uintptr(298)
SYS_futimesat                    :: uintptr(299)
SYS_fstatat64                    :: uintptr(300)
SYS_unlinkat                     :: uintptr(301)
SYS_renameat                     :: uintptr(302)
SYS_linkat                       :: uintptr(303)
SYS_symlinkat                    :: uintptr(304)
SYS_readlinkat                   :: uintptr(305)
SYS_fchmodat                     :: uintptr(306)
SYS_faccessat                    :: uintptr(307)
SYS_pselect6                     :: uintptr(308)
SYS_ppoll                        :: uintptr(309)
SYS_unshare                      :: uintptr(310)
SYS_set_robust_list              :: uintptr(311)
SYS_get_robust_list              :: uintptr(312)
SYS_splice                       :: uintptr(313)
SYS_sync_file_range              :: uintptr(314)
SYS_tee                          :: uintptr(315)
SYS_vmsplice                     :: uintptr(316)
SYS_move_pages                   :: uintptr(317)
SYS_getcpu                       :: uintptr(318)
SYS_epoll_pwait                  :: uintptr(319)
SYS_utimensat                    :: uintptr(320)
SYS_signalfd                     :: uintptr(321)
SYS_timerfd_create               :: uintptr(322)
SYS_eventfd                      :: uintptr(323)
SYS_fallocate                    :: uintptr(324)
SYS_timerfd_settime              :: uintptr(325)
SYS_timerfd_gettime              :: uintptr(326)
SYS_signalfd4                    :: uintptr(327)
SYS_eventfd2                     :: uintptr(328)
SYS_epoll_create1                :: uintptr(329)
SYS_dup3                         :: uintptr(330)
SYS_pipe2                        :: uintptr(331)
SYS_inotify_init1                :: uintptr(332)
SYS_preadv                       :: uintptr(333)
SYS_pwritev                      :: uintptr(334)
SYS_rt_tgsigqueueinfo            :: uintptr(335)
SYS_perf_event_open              :: uintptr(336)
SYS_recvmmsg                     :: uintptr(337)
SYS_fanotify_init                :: uintptr(338)
SYS_fanotify_mark                :: uintptr(339)
SYS_prlimit64                    :: uintptr(340)
SYS_name_to_handle_at            :: uintptr(341)
SYS_open_by_handle_at            :: uintptr(342)
SYS_clock_adjtime                :: uintptr(343)
SYS_syncfs                       :: uintptr(344)
SYS_sendmmsg                     :: uintptr(345)
SYS_setns                        :: uintptr(346)
SYS_process_vm_readv             :: uintptr(347)
SYS_process_vm_writev            :: uintptr(348)
SYS_kcmp                         :: uintptr(349)
SYS_finit_module                 :: uintptr(350)
SYS_sched_setattr                :: uintptr(351)
SYS_sched_getattr                :: uintptr(352)
SYS_renameat2                    :: uintptr(353)
SYS_seccomp                      :: uintptr(354)
SYS_getrandom                    :: uintptr(355)
SYS_memfd_create                 :: uintptr(356)
SYS_bpf                          :: uintptr(357)
SYS_execveat                     :: uintptr(358)
SYS_socket                       :: uintptr(359)
SYS_socketpair                   :: uintptr(360)
SYS_bind                         :: uintptr(361)
SYS_connect                      :: uintptr(362)
SYS_listen                       :: uintptr(363)
SYS_accept4                      :: uintptr(364)
SYS_getsockopt                   :: uintptr(365)
SYS_setsockopt                   :: uintptr(366)
SYS_getsockname                  :: uintptr(367)
SYS_getpeername                  :: uintptr(368)
SYS_sendto                       :: uintptr(369)
SYS_sendmsg                      :: uintptr(370)
SYS_recvfrom                     :: uintptr(371)
SYS_recvmsg                      :: uintptr(372)
SYS_shutdown                     :: uintptr(373)
SYS_userfaultfd                  :: uintptr(374)
SYS_membarrier                   :: uintptr(375)
SYS_mlock2                       :: uintptr(376)
SYS_copy_file_range              :: uintptr(377)
SYS_preadv2                      :: uintptr(378)
SYS_pwritev2                     :: uintptr(379)
SYS_pkey_mprotect                :: uintptr(380)
SYS_pkey_alloc                   :: uintptr(381)
SYS_pkey_free                    :: uintptr(382)
SYS_statx                        :: uintptr(383)
SYS_arch_prctl                   :: uintptr(384)
SYS_io_pgetevents                :: uintptr(385)
SYS_rseq                         :: uintptr(386)
SYS_semget                       :: uintptr(393)
SYS_semctl                       :: uintptr(394)
SYS_shmget                       :: uintptr(395)
SYS_shmctl                       :: uintptr(396)
SYS_shmat                        :: uintptr(397)
SYS_shmdt                        :: uintptr(398)
SYS_msgget                       :: uintptr(399)
SYS_msgsnd                       :: uintptr(400)
SYS_msgrcv                       :: uintptr(401)
SYS_msgctl                       :: uintptr(402)
SYS_clock_gettime64              :: uintptr(403)
SYS_clock_settime64              :: uintptr(404)
SYS_clock_adjtime64              :: uintptr(405)
SYS_clock_getres_time64          :: uintptr(406)
SYS_clock_nanosleep_time64       :: uintptr(407)
SYS_timer_gettime64              :: uintptr(408)
SYS_timer_settime64              :: uintptr(409)
SYS_timerfd_gettime64            :: uintptr(410)
SYS_timerfd_settime64            :: uintptr(411)
SYS_utimensat_time64             :: uintptr(412)
SYS_pselect6_time64              :: uintptr(413)
SYS_ppoll_time64                 :: uintptr(414)
SYS_io_pgetevents_time64         :: uintptr(416)
SYS_recvmmsg_time64              :: uintptr(417)
SYS_mq_timedsend_time64          :: uintptr(418)
SYS_mq_timedreceive_time64       :: uintptr(419)
SYS_semtimedop_time64            :: uintptr(420)
SYS_rt_sigtimedwait_time64       :: uintptr(421)
SYS_futex_time64                 :: uintptr(422)
SYS_sched_rr_get_interval_time64 :: uintptr(423)
SYS_pidfd_send_signal            :: uintptr(424)
SYS_io_uring_setup               :: uintptr(425)
SYS_io_uring_enter               :: uintptr(426)
SYS_io_uring_register            :: uintptr(427)
SYS_open_tree                    :: uintptr(428)
SYS_move_mount                   :: uintptr(429)
SYS_fsopen                       :: uintptr(430)
SYS_fsconfig                     :: uintptr(431)
SYS_fsmount                      :: uintptr(432)
SYS_fspick                       :: uintptr(433)
SYS_pidfd_open                   :: uintptr(434)
SYS_clone3                       :: uintptr(435)
SYS_close_range                  :: uintptr(436)
SYS_openat2                      :: uintptr(437)
SYS_pidfd_getfd                  :: uintptr(438)
SYS_faccessat2                   :: uintptr(439)
SYS_process_madvise              :: uintptr(440)
SYS_epoll_pwait2                 :: uintptr(441)
SYS_mount_setattr                :: uintptr(442)
SYS_quotactl_fd                  :: uintptr(443)
SYS_landlock_create_ruleset      :: uintptr(444)
SYS_landlock_add_rule            :: uintptr(445)
SYS_landlock_restrict_self       :: uintptr(446)
SYS_memfd_secret                 :: uintptr(447)
SYS_process_mrelease             :: uintptr(448)
SYS_futex_waitv                  :: uintptr(449)
SYS_set_mempolicy_home_node      :: uintptr(450)
SYS_cachestat                    :: uintptr(451)
SYS_fchmodat2                    :: uintptr(452)
