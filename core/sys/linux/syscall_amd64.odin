#+build amd64
package linux

// AMD64 uses the new way to define syscalls, i.e. one that
// is different from the other architectures. Instead of using
// a .tbl file, they define constants to tell which syscalls they
// want and then include a generic unistd.h file.

SYS_read                    :: uintptr(0)
SYS_write                   :: uintptr(1)
SYS_open                    :: uintptr(2)
SYS_close                   :: uintptr(3)
SYS_stat                    :: uintptr(4)
SYS_fstat                   :: uintptr(5)
SYS_lstat                   :: uintptr(6)
SYS_poll                    :: uintptr(7)
SYS_lseek                   :: uintptr(8)
SYS_mmap                    :: uintptr(9)
SYS_mprotect                :: uintptr(10)
SYS_munmap                  :: uintptr(11)
SYS_brk                     :: uintptr(12)
SYS_rt_sigaction            :: uintptr(13)
SYS_rt_sigprocmask          :: uintptr(14)
SYS_rt_sigreturn            :: uintptr(15)
SYS_ioctl                   :: uintptr(16)
SYS_pread64                 :: uintptr(17)
SYS_pwrite64                :: uintptr(18)
SYS_readv                   :: uintptr(19)
SYS_writev                  :: uintptr(20)
SYS_access                  :: uintptr(21)
SYS_pipe                    :: uintptr(22)
SYS_select                  :: uintptr(23)
SYS_sched_yield             :: uintptr(24)
SYS_mremap                  :: uintptr(25)
SYS_msync                   :: uintptr(26)
SYS_mincore                 :: uintptr(27)
SYS_madvise                 :: uintptr(28)
SYS_shmget                  :: uintptr(29)
SYS_shmat                   :: uintptr(30)
SYS_shmctl                  :: uintptr(31)
SYS_dup                     :: uintptr(32)
SYS_dup2                    :: uintptr(33)
SYS_pause                   :: uintptr(34)
SYS_nanosleep               :: uintptr(35)
SYS_getitimer               :: uintptr(36)
SYS_alarm                   :: uintptr(37)
SYS_setitimer               :: uintptr(38)
SYS_getpid                  :: uintptr(39)
SYS_sendfile                :: uintptr(40)
SYS_socket                  :: uintptr(41)
SYS_connect                 :: uintptr(42)
SYS_accept                  :: uintptr(43)
SYS_sendto                  :: uintptr(44)
SYS_recvfrom                :: uintptr(45)
SYS_sendmsg                 :: uintptr(46)
SYS_recvmsg                 :: uintptr(47)
SYS_shutdown                :: uintptr(48)
SYS_bind                    :: uintptr(49)
SYS_listen                  :: uintptr(50)
SYS_getsockname             :: uintptr(51)
SYS_getpeername             :: uintptr(52)
SYS_socketpair              :: uintptr(53)
SYS_setsockopt              :: uintptr(54)
SYS_getsockopt              :: uintptr(55)
SYS_clone                   :: uintptr(56)
SYS_fork                    :: uintptr(57)
SYS_vfork                   :: uintptr(58)
SYS_execve                  :: uintptr(59)
SYS_exit                    :: uintptr(60)
SYS_wait4                   :: uintptr(61)
SYS_kill                    :: uintptr(62)
SYS_uname                   :: uintptr(63)
SYS_semget                  :: uintptr(64)
SYS_semop                   :: uintptr(65)
SYS_semctl                  :: uintptr(66)
SYS_shmdt                   :: uintptr(67)
SYS_msgget                  :: uintptr(68)
SYS_msgsnd                  :: uintptr(69)
SYS_msgrcv                  :: uintptr(70)
SYS_msgctl                  :: uintptr(71)
SYS_fcntl                   :: uintptr(72)
SYS_flock                   :: uintptr(73)
SYS_fsync                   :: uintptr(74)
SYS_fdatasync               :: uintptr(75)
SYS_truncate                :: uintptr(76)
SYS_ftruncate               :: uintptr(77)
SYS_getdents                :: uintptr(78)
SYS_getcwd                  :: uintptr(79)
SYS_chdir                   :: uintptr(80)
SYS_fchdir                  :: uintptr(81)
SYS_rename                  :: uintptr(82)
SYS_mkdir                   :: uintptr(83)
SYS_rmdir                   :: uintptr(84)
SYS_creat                   :: uintptr(85)
SYS_link                    :: uintptr(86)
SYS_unlink                  :: uintptr(87)
SYS_symlink                 :: uintptr(88)
SYS_readlink                :: uintptr(89)
SYS_chmod                   :: uintptr(90)
SYS_fchmod                  :: uintptr(91)
SYS_chown                   :: uintptr(92)
SYS_fchown                  :: uintptr(93)
SYS_lchown                  :: uintptr(94)
SYS_umask                   :: uintptr(95)
SYS_gettimeofday            :: uintptr(96)
SYS_getrlimit               :: uintptr(97)
SYS_getrusage               :: uintptr(98)
SYS_sysinfo                 :: uintptr(99)
SYS_times                   :: uintptr(100)
SYS_ptrace                  :: uintptr(101)
SYS_getuid                  :: uintptr(102)
SYS_syslog                  :: uintptr(103)
SYS_getgid                  :: uintptr(104)
SYS_setuid                  :: uintptr(105)
SYS_setgid                  :: uintptr(106)
SYS_geteuid                 :: uintptr(107)
SYS_getegid                 :: uintptr(108)
SYS_setpgid                 :: uintptr(109)
SYS_getppid                 :: uintptr(110)
SYS_getpgrp                 :: uintptr(111)
SYS_setsid                  :: uintptr(112)
SYS_setreuid                :: uintptr(113)
SYS_setregid                :: uintptr(114)
SYS_getgroups               :: uintptr(115)
SYS_setgroups               :: uintptr(116)
SYS_setresuid               :: uintptr(117)
SYS_getresuid               :: uintptr(118)
SYS_setresgid               :: uintptr(119)
SYS_getresgid               :: uintptr(120)
SYS_getpgid                 :: uintptr(121)
SYS_setfsuid                :: uintptr(122)
SYS_setfsgid                :: uintptr(123)
SYS_getsid                  :: uintptr(124)
SYS_capget                  :: uintptr(125)
SYS_capset                  :: uintptr(126)
SYS_rt_sigpending           :: uintptr(127)
SYS_rt_sigtimedwait         :: uintptr(128)
SYS_rt_sigqueueinfo         :: uintptr(129)
SYS_rt_sigsuspend           :: uintptr(130)
SYS_sigaltstack             :: uintptr(131)
SYS_utime                   :: uintptr(132)
SYS_mknod                   :: uintptr(133)
SYS_uselib                  :: uintptr(134)
SYS_personality             :: uintptr(135)
SYS_ustat                   :: uintptr(136)
SYS_statfs                  :: uintptr(137)
SYS_fstatfs                 :: uintptr(138)
SYS_sysfs                   :: uintptr(139)
SYS_getpriority             :: uintptr(140)
SYS_setpriority             :: uintptr(141)
SYS_sched_setparam          :: uintptr(142)
SYS_sched_getparam          :: uintptr(143)
SYS_sched_setscheduler      :: uintptr(144)
SYS_sched_getscheduler      :: uintptr(145)
SYS_sched_get_priority_max  :: uintptr(146)
SYS_sched_get_priority_min  :: uintptr(147)
SYS_sched_rr_get_interval   :: uintptr(148)
SYS_mlock                   :: uintptr(149)
SYS_munlock                 :: uintptr(150)
SYS_mlockall                :: uintptr(151)
SYS_munlockall              :: uintptr(152)
SYS_vhangup                 :: uintptr(153)
SYS_modify_ldt              :: uintptr(154)
SYS_pivot_root              :: uintptr(155)
SYS__sysctl                 :: uintptr(156)
SYS_prctl                   :: uintptr(157)
SYS_arch_prctl              :: uintptr(158)
SYS_adjtimex                :: uintptr(159)
SYS_setrlimit               :: uintptr(160)
SYS_chroot                  :: uintptr(161)
SYS_sync                    :: uintptr(162)
SYS_acct                    :: uintptr(163)
SYS_settimeofday            :: uintptr(164)
SYS_mount                   :: uintptr(165)
SYS_umount2                 :: uintptr(166)
SYS_swapon                  :: uintptr(167)
SYS_swapoff                 :: uintptr(168)
SYS_reboot                  :: uintptr(169)
SYS_sethostname             :: uintptr(170)
SYS_setdomainname           :: uintptr(171)
SYS_iopl                    :: uintptr(172)
SYS_ioperm                  :: uintptr(173)
SYS_create_module           :: uintptr(174)
SYS_init_module             :: uintptr(175)
SYS_delete_module           :: uintptr(176)
SYS_get_kernel_syms         :: uintptr(177)
SYS_query_module            :: uintptr(178)
SYS_quotactl                :: uintptr(179)
SYS_nfsservctl              :: uintptr(180)
SYS_getpmsg                 :: uintptr(181)
SYS_putpmsg                 :: uintptr(182)
SYS_afs_syscall             :: uintptr(183)
SYS_tuxcall                 :: uintptr(184)
SYS_security                :: uintptr(185)
SYS_gettid                  :: uintptr(186)
SYS_readahead               :: uintptr(187)
SYS_setxattr                :: uintptr(188)
SYS_lsetxattr               :: uintptr(189)
SYS_fsetxattr               :: uintptr(190)
SYS_getxattr                :: uintptr(191)
SYS_lgetxattr               :: uintptr(192)
SYS_fgetxattr               :: uintptr(193)
SYS_listxattr               :: uintptr(194)
SYS_llistxattr              :: uintptr(195)
SYS_flistxattr              :: uintptr(196)
SYS_removexattr             :: uintptr(197)
SYS_lremovexattr            :: uintptr(198)
SYS_fremovexattr            :: uintptr(199)
SYS_tkill                   :: uintptr(200)
SYS_time                    :: uintptr(201)
SYS_futex                   :: uintptr(202)
SYS_sched_setaffinity       :: uintptr(203)
SYS_sched_getaffinity       :: uintptr(204)
SYS_set_thread_area         :: uintptr(205)
SYS_io_setup                :: uintptr(206)
SYS_io_destroy              :: uintptr(207)
SYS_io_getevents            :: uintptr(208)
SYS_io_submit               :: uintptr(209)
SYS_io_cancel               :: uintptr(210)
SYS_get_thread_area         :: uintptr(211)
SYS_lookup_dcookie          :: uintptr(212)
SYS_epoll_create            :: uintptr(213)
SYS_epoll_ctl_old           :: uintptr(214)
SYS_epoll_wait_old          :: uintptr(215)
SYS_remap_file_pages        :: uintptr(216)
SYS_getdents64              :: uintptr(217)
SYS_set_tid_address         :: uintptr(218)
SYS_restart_syscall         :: uintptr(219)
SYS_semtimedop              :: uintptr(220)
SYS_fadvise64               :: uintptr(221)
SYS_timer_create            :: uintptr(222)
SYS_timer_settime           :: uintptr(223)
SYS_timer_gettime           :: uintptr(224)
SYS_timer_getoverrun        :: uintptr(225)
SYS_timer_delete            :: uintptr(226)
SYS_clock_settime           :: uintptr(227)
SYS_clock_gettime           :: uintptr(228)
SYS_clock_getres            :: uintptr(229)
SYS_clock_nanosleep         :: uintptr(230)
SYS_exit_group              :: uintptr(231)
SYS_epoll_wait              :: uintptr(232)
SYS_epoll_ctl               :: uintptr(233)
SYS_tgkill                  :: uintptr(234)
SYS_utimes                  :: uintptr(235)
SYS_vserver                 :: uintptr(236)
SYS_mbind                   :: uintptr(237)
SYS_set_mempolicy           :: uintptr(238)
SYS_get_mempolicy           :: uintptr(239)
SYS_mq_open                 :: uintptr(240)
SYS_mq_unlink               :: uintptr(241)
SYS_mq_timedsend            :: uintptr(242)
SYS_mq_timedreceive         :: uintptr(243)
SYS_mq_notify               :: uintptr(244)
SYS_mq_getsetattr           :: uintptr(245)
SYS_kexec_load              :: uintptr(246)
SYS_waitid                  :: uintptr(247)
SYS_add_key                 :: uintptr(248)
SYS_request_key             :: uintptr(249)
SYS_keyctl                  :: uintptr(250)
SYS_ioprio_set              :: uintptr(251)
SYS_ioprio_get              :: uintptr(252)
SYS_inotify_init            :: uintptr(253)
SYS_inotify_add_watch       :: uintptr(254)
SYS_inotify_rm_watch        :: uintptr(255)
SYS_migrate_pages           :: uintptr(256)
SYS_openat                  :: uintptr(257)
SYS_mkdirat                 :: uintptr(258)
SYS_mknodat                 :: uintptr(259)
SYS_fchownat                :: uintptr(260)
SYS_futimesat               :: uintptr(261)
SYS_newfstatat              :: uintptr(262)
SYS_unlinkat                :: uintptr(263)
SYS_renameat                :: uintptr(264)
SYS_linkat                  :: uintptr(265)
SYS_symlinkat               :: uintptr(266)
SYS_readlinkat              :: uintptr(267)
SYS_fchmodat                :: uintptr(268)
SYS_faccessat               :: uintptr(269)
SYS_pselect6                :: uintptr(270)
SYS_ppoll                   :: uintptr(271)
SYS_unshare                 :: uintptr(272)
SYS_set_robust_list         :: uintptr(273)
SYS_get_robust_list         :: uintptr(274)
SYS_splice                  :: uintptr(275)
SYS_tee                     :: uintptr(276)
SYS_sync_file_range         :: uintptr(277)
SYS_vmsplice                :: uintptr(278)
SYS_move_pages              :: uintptr(279)
SYS_utimensat               :: uintptr(280)
SYS_epoll_pwait             :: uintptr(281)
SYS_signalfd                :: uintptr(282)
SYS_timerfd_create          :: uintptr(283)
SYS_eventfd                 :: uintptr(284)
SYS_fallocate               :: uintptr(285)
SYS_timerfd_settime         :: uintptr(286)
SYS_timerfd_gettime         :: uintptr(287)
SYS_accept4                 :: uintptr(288)
SYS_signalfd4               :: uintptr(289)
SYS_eventfd2                :: uintptr(290)
SYS_epoll_create1           :: uintptr(291)
SYS_dup3                    :: uintptr(292)
SYS_pipe2                   :: uintptr(293)
SYS_inotify_init1           :: uintptr(294)
SYS_preadv                  :: uintptr(295)
SYS_pwritev                 :: uintptr(296)
SYS_rt_tgsigqueueinfo       :: uintptr(297)
SYS_perf_event_open         :: uintptr(298)
SYS_recvmmsg                :: uintptr(299)
SYS_fanotify_init           :: uintptr(300)
SYS_fanotify_mark           :: uintptr(301)
SYS_prlimit64               :: uintptr(302)
SYS_name_to_handle_at       :: uintptr(303)
SYS_open_by_handle_at       :: uintptr(304)
SYS_clock_adjtime           :: uintptr(305)
SYS_syncfs                  :: uintptr(306)
SYS_sendmmsg                :: uintptr(307)
SYS_setns                   :: uintptr(308)
SYS_getcpu                  :: uintptr(309)
SYS_process_vm_readv        :: uintptr(310)
SYS_process_vm_writev       :: uintptr(311)
SYS_kcmp                    :: uintptr(312)
SYS_finit_module            :: uintptr(313)
SYS_sched_setattr           :: uintptr(314)
SYS_sched_getattr           :: uintptr(315)
SYS_renameat2               :: uintptr(316)
SYS_seccomp                 :: uintptr(317)
SYS_getrandom               :: uintptr(318)
SYS_memfd_create            :: uintptr(319)
SYS_kexec_file_load         :: uintptr(320)
SYS_bpf                     :: uintptr(321)
SYS_execveat                :: uintptr(322)
SYS_userfaultfd             :: uintptr(323)
SYS_membarrier              :: uintptr(324)
SYS_mlock2                  :: uintptr(325)
SYS_copy_file_range         :: uintptr(326)
SYS_preadv2                 :: uintptr(327)
SYS_pwritev2                :: uintptr(328)
SYS_pkey_mprotect           :: uintptr(329)
SYS_pkey_alloc              :: uintptr(330)
SYS_pkey_free               :: uintptr(331)
SYS_statx                   :: uintptr(332)
SYS_io_pgetevents           :: uintptr(333)
SYS_rseq                    :: uintptr(334)
SYS_pidfd_send_signal       :: uintptr(424)
SYS_io_uring_setup          :: uintptr(425)
SYS_io_uring_enter          :: uintptr(426)
SYS_io_uring_register       :: uintptr(427)
SYS_open_tree               :: uintptr(428)
SYS_move_mount              :: uintptr(429)
SYS_fsopen                  :: uintptr(430)
SYS_fsconfig                :: uintptr(431)
SYS_fsmount                 :: uintptr(432)
SYS_fspick                  :: uintptr(433)
SYS_pidfd_open              :: uintptr(434)
SYS_clone3                  :: uintptr(435)
SYS_close_range             :: uintptr(436)
SYS_openat2                 :: uintptr(437)
SYS_pidfd_getfd             :: uintptr(438)
SYS_faccessat2              :: uintptr(439)
SYS_process_madvise         :: uintptr(440)
SYS_epoll_pwait2            :: uintptr(441)
SYS_mount_setattr           :: uintptr(442)
SYS_quotactl_fd             :: uintptr(443)
SYS_landlock_create_ruleset :: uintptr(444)
SYS_landlock_add_rule       :: uintptr(445)
SYS_landlock_restrict_self  :: uintptr(446)
SYS_memfd_secret            :: uintptr(447)
SYS_process_mrelease        :: uintptr(448)
SYS_futex_waitv             :: uintptr(449)
SYS_set_mempolicy_home_node :: uintptr(450)
SYS_cachestat               :: uintptr(451)
SYS_fchmodat2               :: uintptr(452)
SYS_map_shadow_stack        :: uintptr(453)
