package es
ElementPublic :: struct {
	userCallback :   UICallbackFunction,
	cName :   cstring  ,
	userData :   Generic,
	accessKey :   i8  ,
	window :   ^Window,
	instance :   ^INSTANCE_TYPE,
	flags :   u64     ,
}
Generic :: rawptr;
INSTANCE_TYPE :: Instance;
Element :: ElementPublic;
Panel :: Element;
Window :: Element;
Scrollbar :: Element;
Button :: Element;
TextDisplay :: Element;
IconDisplay :: Element;
Textbox :: Element;
ListView :: Element;
Menu :: Element;
Choice :: Element;
ColorWell :: Element;
Splitter :: Element;
ImageDisplay :: Element;
TextPlan :: rawptr;
Store :: rawptr;
PaintTarget :: rawptr;
DirectoryMonitor :: rawptr;
NodeType :: u8     ;
Error :: int     ;
Handle :: uint     ;
Response :: i32;
FileOffset :: u64     ;
FileOffsetDifference :: i64    ;
AudioDeviceID :: u64     ;
SCANCODE_A :: 				(0x04);
SCANCODE_B :: 				(0x05);
SCANCODE_C :: 				(0x06);
SCANCODE_D :: 				(0x07);
SCANCODE_E :: 				(0x08);
SCANCODE_F :: 				(0x09);
SCANCODE_G :: 				(0x0A);
SCANCODE_H :: 				(0x0B);
SCANCODE_I :: 				(0x0C);
SCANCODE_J :: 				(0x0D);
SCANCODE_K :: 				(0x0E);
SCANCODE_L :: 				(0x0F);
SCANCODE_M :: 				(0x10);
SCANCODE_N :: 				(0x11);
SCANCODE_O :: 				(0x12);
SCANCODE_P :: 				(0x13);
SCANCODE_Q :: 				(0x14);
SCANCODE_R :: 				(0x15);
SCANCODE_S :: 				(0x16);
SCANCODE_T :: 				(0x17);
SCANCODE_U :: 				(0x18);
SCANCODE_V :: 				(0x19);
SCANCODE_W :: 				(0x1A);
SCANCODE_X :: 				(0x1B);
SCANCODE_Y :: 				(0x1C);
SCANCODE_Z :: 				(0x1D);
SCANCODE_1 :: 				(0x1E);
SCANCODE_2 :: 				(0x1F);
SCANCODE_3 :: 				(0x20);
SCANCODE_4 :: 				(0x21);
SCANCODE_5 :: 				(0x22);
SCANCODE_6 :: 				(0x23);
SCANCODE_7 :: 				(0x24);
SCANCODE_8 :: 				(0x25);
SCANCODE_9 :: 				(0x26);
SCANCODE_0 :: 				(0x27);
SCANCODE_ENTER ::  			(0x28);
SCANCODE_ESCAPE :: 			(0x29);
SCANCODE_BACKSPACE :: 			(0x2A);
SCANCODE_TAB :: 				(0x2B);
SCANCODE_SPACE :: 			(0x2C);
SCANCODE_HYPHEN :: 			(0x2D);
SCANCODE_EQUALS :: 			(0x2E);
SCANCODE_LEFT_BRACE :: 			(0x2F);
SCANCODE_RIGHT_BRACE :: 			(0x30);
SCANCODE_COMMA :: 			(0x36);
SCANCODE_PERIOD :: 			(0x37);
SCANCODE_SLASH :: 			(0x38);
SCANCODE_PUNCTUATION_1 :: 		(0x31) ;
SCANCODE_PUNCTUATION_2 :: 		(0x32) ;
SCANCODE_PUNCTUATION_3 :: 		(0x33) ;
SCANCODE_PUNCTUATION_4 :: 		(0x34) ;
SCANCODE_PUNCTUATION_5 :: 		(0x35) ;
SCANCODE_PUNCTUATION_6 :: 		(0x64) ;
SCANCODE_F1 :: 				(0x3A);
SCANCODE_F2 :: 				(0x3B);
SCANCODE_F3 :: 				(0x3C);
SCANCODE_F4 :: 				(0x3D);
SCANCODE_F5 :: 				(0x3E);
SCANCODE_F6 :: 				(0x3F);
SCANCODE_F7 :: 				(0x40);
SCANCODE_F8 :: 				(0x41);
SCANCODE_F9 :: 				(0x42);
SCANCODE_F10 :: 				(0x43);
SCANCODE_F11 :: 				(0x44);
SCANCODE_F12 :: 				(0x45);
SCANCODE_F13 :: 				(0x68);
SCANCODE_F14 :: 				(0x69);
SCANCODE_F15 :: 				(0x6A);
SCANCODE_F16 :: 				(0x6B);
SCANCODE_F17 :: 				(0x6C);
SCANCODE_F18 :: 				(0x6D);
SCANCODE_F19 :: 				(0x6E);
SCANCODE_F20 :: 				(0x6F);
SCANCODE_F21 :: 				(0x70);
SCANCODE_F22 :: 				(0x71);
SCANCODE_F23 :: 				(0x72);
SCANCODE_F24 :: 				(0x73);
SCANCODE_CAPS_LOCK :: 			(0x39);
SCANCODE_PRINT_SCREEN :: 			(0x46);
SCANCODE_SCROLL_LOCK :: 			(0x47);
SCANCODE_PAUSE :: 			(0x48);
SCANCODE_INSERT :: 			(0x49);
SCANCODE_HOME :: 				(0x4A);
SCANCODE_PAGE_UP :: 			(0x4B);
SCANCODE_DELETE :: 			(0x4C);
SCANCODE_END :: 				(0x4D);
SCANCODE_PAGE_DOWN :: 			(0x4E);
SCANCODE_RIGHT_ARROW :: 			(0x4F);
SCANCODE_LEFT_ARROW :: 			(0x50);
SCANCODE_DOWN_ARROW :: 			(0x51);
SCANCODE_UP_ARROW :: 			(0x52);
SCANCODE_NUM_LOCK :: 			(0x53);
SCANCODE_CONTEXT_MENU ::  		(0x65);
SCANCODE_SYSTEM_REQUEST :: 		(0x9A);
SCANCODE_ACTION_EXECUTE :: 		(0x74);
SCANCODE_ACTION_HELP :: 			(0x75);
SCANCODE_ACTION_MENU :: 			(0x76);
SCANCODE_ACTION_SELECT :: 		(0x77);
SCANCODE_ACTION_STOP :: 			(0x78);
SCANCODE_ACTION_AGAIN :: 			(0x79);
SCANCODE_ACTION_UNDO :: 			(0x7A);
SCANCODE_ACTION_CUT :: 			(0x7B);
SCANCODE_ACTION_COPY :: 			(0x7C);
SCANCODE_ACTION_PASTE :: 			(0x7D);
SCANCODE_ACTION_FIND :: 			(0x7E);
SCANCODE_ACTION_CANCEL :: 		(0x9B);
SCANCODE_ACTION_CLEAR :: 			(0x9C);
SCANCODE_ACTION_PRIOR :: 			(0x9D);
SCANCODE_ACTION_RETURN :: 		(0x9E);
SCANCODE_ACTION_SEPARATOR :: 		(0x9F);
SCANCODE_MM_MUTE :: 			(0x7F);
SCANCODE_MM_LOUDER :: 			(0x80);
SCANCODE_MM_QUIETER :: 			(0x81);
SCANCODE_MM_NEXT :: 			(0x103);
SCANCODE_MM_PREVIOUS :: 			(0x104);
SCANCODE_MM_STOP :: 			(0x105);
SCANCODE_MM_PAUSE :: 			(0x106);
SCANCODE_MM_SELECT :: 			(0x107);
SCANCODE_MM_EMAIL :: 			(0x108);
SCANCODE_MM_CALC :: 			(0x109);
SCANCODE_MM_FILES :: 			(0x10A);
SCANCODE_INTERNATIONAL_1 :: 		(0x87);
SCANCODE_INTERNATIONAL_2 :: 		(0x88);
SCANCODE_INTERNATIONAL_3 :: 		(0x89);
SCANCODE_INTERNATIONAL_4 :: 		(0x8A);
SCANCODE_INTERNATIONAL_5 :: 		(0x8B);
SCANCODE_INTERNATIONAL_6 :: 		(0x8C);
SCANCODE_INTERNATIONAL_7 :: 		(0x8D);
SCANCODE_INTERNATIONAL_8 :: 		(0x8E);
SCANCODE_INTERNATIONAL_9 :: 		(0x8F);
SCANCODE_HANGUL_ENGLISH_TOGGLE :: 	(0x90);
SCANCODE_HANJA_CONVERSION :: 		(0x91);
SCANCODE_KATAKANA :: 			(0x92);
SCANCODE_HIRAGANA :: 			(0x93);
SCANCODE_HANKAKU_ZENKAKU_TOGGLE :: 	(0x94);
SCANCODE_ALTERNATE_ERASE :: 		(0x99);
SCANCODE_THOUSANDS_SEPARATOR :: 		(0xB2);
SCANCODE_DECIMAL_SEPARATOR :: 		(0xB3);
SCANCODE_CURRENCY_UNIT :: 		(0xB4);
SCANCODE_CURRENCY_SUBUNIT :: 		(0xB5);
SCANCODE_NUM_DIVIDE :: 			(0x54);
SCANCODE_NUM_MULTIPLY :: 			(0x55);
SCANCODE_NUM_SUBTRACT :: 			(0x56);
SCANCODE_NUM_ADD :: 			(0x57);
SCANCODE_NUM_ENTER :: 			(0x58);
SCANCODE_NUM_1 :: 			(0x59);
SCANCODE_NUM_2 :: 			(0x5A);
SCANCODE_NUM_3 :: 			(0x5B);
SCANCODE_NUM_4 :: 			(0x5C);
SCANCODE_NUM_5 :: 			(0x5D);
SCANCODE_NUM_6 :: 			(0x5E);
SCANCODE_NUM_7 :: 			(0x5F);
SCANCODE_NUM_8 :: 			(0x60);
SCANCODE_NUM_9 :: 			(0x61);
SCANCODE_NUM_0 :: 			(0x62);
SCANCODE_NUM_POINT :: 			(0x63);
SCANCODE_NUM_EQUALS :: 			(0x67);
SCANCODE_NUM_COMMA :: 			(0x82);
SCANCODE_NUM_00 :: 			(0xB0);
SCANCODE_NUM_000 :: 			(0xB1);
SCANCODE_NUM_LEFT_PAREN :: 		(0xB6);
SCANCODE_NUM_RIGHT_PAREN :: 		(0xB7);
SCANCODE_NUM_LEFT_BRACE :: 		(0xB8);
SCANCODE_NUM_RIGHT_BRACE :: 		(0xB9);
SCANCODE_NUM_TAB :: 			(0xBA);
SCANCODE_NUM_BACKSPACE :: 		(0xBB);
SCANCODE_NUM_A :: 			(0xBC);
SCANCODE_NUM_B :: 			(0xBD);
SCANCODE_NUM_C :: 			(0xBE);
SCANCODE_NUM_D :: 			(0xBF);
SCANCODE_NUM_E :: 			(0xC0);
SCANCODE_NUM_F :: 			(0xC1);
SCANCODE_NUM_XOR :: 			(0xC2);
SCANCODE_NUM_CARET :: 			(0xC3);
SCANCODE_NUM_PERCENT :: 			(0xC4);
SCANCODE_NUM_LESS_THAN :: 		(0xC5);
SCANCODE_NUM_GREATER_THAN :: 		(0xC6);
SCANCODE_NUM_AMPERSAND :: 		(0xC7);
SCANCODE_NUM_DOUBLE_AMPERSAND :: 		(0xC8);
SCANCODE_NUM_BAR :: 			(0xC9);
SCANCODE_NUM_DOUBLE_BAR :: 		(0xCA);
SCANCODE_NUM_COLON :: 			(0xCB);
SCANCODE_NUM_HASH :: 			(0xCC);
SCANCODE_NUM_SPACE :: 			(0xCD);
SCANCODE_NUM_AT :: 			(0xCE);
SCANCODE_NUM_EXCLAMATION_MARK :: 		(0xCF);
SCANCODE_NUM_MEMORY_STORE :: 		(0xD0);
SCANCODE_NUM_MEMORY_RECALL :: 		(0xD1);
SCANCODE_NUM_MEMORY_CLEAR :: 		(0xD2);
SCANCODE_NUM_MEMORY_ADD :: 		(0xD3);
SCANCODE_NUM_MEMORY_SUBTRACT :: 		(0xD4);
SCANCODE_NUM_MEMORY_MULTIPLY :: 		(0xD5);
SCANCODE_NUM_MEMORY_DIVIDE :: 		(0xD6);
SCANCODE_NUM_NEGATE :: 			(0xD7);
SCANCODE_NUM_CLEAR_ALL :: 		(0xD8);
SCANCODE_NUM_CLEAR :: 			(0xD9);
SCANCODE_NUM_BINARY :: 			(0xDA);
SCANCODE_NUM_OCTAL :: 			(0xDB);
SCANCODE_NUM_DECIMAL :: 			(0xDC);
SCANCODE_NUM_HEXADECIMAL :: 		(0xDD);
SCANCODE_LEFT_CTRL :: 			(0xE0);
SCANCODE_LEFT_SHIFT :: 			(0xE1);
SCANCODE_LEFT_ALT :: 			(0xE2);
SCANCODE_LEFT_FLAG :: 			(0xE3);
SCANCODE_RIGHT_CTRL :: 			(0xE4);
SCANCODE_RIGHT_SHIFT :: 			(0xE5);
SCANCODE_RIGHT_ALT :: 			(0xE6);
SCANCODE_RIGHT_FLAG :: 			(0xE7);
SCANCODE_ACPI_POWER ::  			(0x100);
SCANCODE_ACPI_SLEEP ::  			(0x101);
SCANCODE_ACPI_WAKE ::   			(0x102);
SCANCODE_WWW_SEARCH :: 			(0x10B);
SCANCODE_WWW_HOME :: 			(0x10C);
SCANCODE_WWW_BACK :: 			(0x10D);
SCANCODE_WWW_FORWARD :: 			(0x10E);
SCANCODE_WWW_STOP :: 			(0x10F);
SCANCODE_WWW_REFRESH :: 			(0x110);
SCANCODE_WWW_STARRED :: 			(0x111);
PROCESS_STATE_ALL_THREADS_TERMINATED :: 	(1);
PROCESS_STATE_TERMINATING :: 		(2);
PROCESS_STATE_CRASHED :: 			(4);
PROCESS_STATE_PINGED :: 			(8);
FLAGS_DEFAULT ::  (0);
SUCCESS ::  	(-1);
ERROR_BUFFER_TOO_SMALL :: 		(-2);
ERROR_UNKNOWN ::  	(-7);
ERROR_NO_MESSAGES_AVAILABLE :: 		(-9);
ERROR_MESSAGE_QUEUE_FULL :: 		(-10);
ERROR_PATH_NOT_WITHIN_MOUNTED_VOLUME :: 	(-14);
ERROR_PATH_NOT_TRAVERSABLE :: 		(-15);
ERROR_FILE_ALREADY_EXISTS :: 		(-19);
ERROR_FILE_DOES_NOT_EXIST :: 		(-20);
ERROR_DRIVE_ERROR_FILE_DAMAGED :: 	(-21) ;
ERROR_ACCESS_NOT_WITHIN_FILE_BOUNDS :: 	(-22) ;
ERROR_FILE_PERMISSION_NOT_GRANTED :: 	(-23);
ERROR_FILE_IN_EXCLUSIVE_USE :: 		(-24);
ERROR_FILE_CANNOT_GET_EXCLUSIVE_USE :: 	(-25);
ERROR_INCORRECT_NODE_TYPE :: 		(-26);
ERROR_EVENT_NOT_SET :: 			(-27);
ERROR_FILE_HAS_WRITERS :: 		(-28);
ERROR_TIMEOUT_REACHED :: 			(-29);
ERROR_FILE_ON_READ_ONLY_VOLUME :: 	(-32);
ERROR_INVALID_DIMENSIONS :: 		(-34);
ERROR_DRIVE_CONTROLLER_REPORTED :: 	(-35);
ERROR_COULD_NOT_ISSUE_PACKET :: 		(-36);
ERROR_HANDLE_TABLE_FULL :: 		(-37);
ERROR_COULD_NOT_RESIZE_FILE :: 		(-38);
ERROR_DIRECTORY_NOT_EMPTY :: 		(-39);
ERROR_NODE_DELETED :: 			(-41);
ERROR_VOLUME_MISMATCH :: 			(-43);
ERROR_TARGET_WITHIN_SOURCE :: 		(-44);
ERROR_TARGET_INVALID_TYPE :: 		(-45);
ERROR_MALFORMED_NODE_PATH :: 		(-47);
ERROR_OUT_OF_CACHE_RESOURCES :: 		(-48);
ERROR_TARGET_IS_SOURCE :: 		(-49);
ERROR_INVALID_NAME :: 			(-50);
ERROR_CORRUPT_DATA :: 			(-51);
ERROR_INSUFFICIENT_RESOURCES :: 		(-52);
ERROR_UNSUPPORTED_FEATURE :: 		(-53);
ERROR_FILE_TOO_FRAGMENTED :: 		(-54);
ERROR_DRIVE_FULL :: 			(-55);
ERROR_COULD_NOT_RESOLVE_SYMBOL :: 	(-56);
ERROR_ALREADY_EMBEDDED :: 		(-57);
ERROR_EVENT_SINK_OVERFLOW ::  		(-58);
ERROR_EVENT_SINK_DUPLICATE :: 		(-59);
ERROR_UNSUPPORTED_CONVERSION :: 		(-60);
ERROR_SOURCE_EMPTY :: 			(-61);
ERROR_UNSUPPORTED_EXECUTABLE :: 		(-62);
ERROR_NO_ADDRESS_FOR_DOMAIN_NAME :: 	(-63);
ERROR_NO_CONNECTED_NETWORK_INTERFACES :: 	(-64);
ERROR_BAD_DOMAIN_NAME :: 			(-65);
ERROR_LOST_IP_ADDRESS :: 			(-66);
ERROR_CONNECTION_RESET :: 		(-67);
ERROR_CONNECTION_REFUSED :: 		(-68);
SYSTEM_CONSTANT_TIME_STAMP_UNITS_PER_MICROSECOND ::  (0);
SYSTEM_CONSTANT_NO_FANCY_GRAPHICS :: 		   (1);
SYSTEM_CONSTANT_REPORTED_PROBLEMS :: 		   (2);
SYSTEM_CONSTANT_RIGHT_TO_LEFT :: 		    	   (3);
SYSTEM_CONSTANT_WINDOW_INSET ::  		    	   (4);
SYSTEM_CONSTANT_CONTAINER_TAB_BAND_HEIGHT ::         (5);
INVALID_HANDLE ::  		((  Handle) (0));
CURRENT_THREAD :: 	 	((  Handle) (0x10));
CURRENT_PROCESS :: 	 	((  Handle) (0x11));
DRAW_ALPHA_OVERWRITE :: 		(0x100);
DRAW_ALPHA_FULL :: 		(0x200) ;
WAIT_NO_TIMEOUT ::             (-1);
MAX_WAIT_COUNT ::              (8);
MAX_EVENT_FORWARD_COUNT ::     (4)   ;
MAX_EVENT_SINK_BUFFER_SIZE ::  (256) ;
MAX_DIRECTORY_CHILD_NAME_LENGTH ::  (256);
PROCESS_EXECUTABLE_NOT_LOADED ::      (0);
PROCESS_EXECUTABLE_FAILED_TO_LOAD ::  (1);
PROCESS_EXECUTABLE_LOADED ::          (2);
SNAPSHOT_MAX_PROCESS_NAME_LENGTH ::  	(80);
SYSTEM_SNAPSHOT_PROCESSES ::  		(1);
SYSTEM_SNAPSHOT_DRIVES ::  		(2);
HANDLED ::      (0);
NOT_HANDLED ::  (-1);
REJECTED ::     (-2);
SHARED_MEMORY_NAME_MAX_LENGTH ::  (32);
MAP_OBJECT_ALL ::  (0);
TEXT_H_LEFT ::  			(1);
TEXT_H_RIGHT ::  			(2);
TEXT_H_CENTER ::  		(3);
TEXT_V_TOP ::  			(4);
TEXT_V_BOTTOM ::  		(8);
TEXT_V_CENTER ::  		(12);
TEXT_WRAP ::  			(16);
TEXT_ELLIPSIS :: 			(32);
NODE_ACCESS_READ_SHARED :: 	(0x1) ;
NODE_ACCESS_READ :: 		(0x2) ;
NODE_ACCESS_WRITE :: 		(0x4) ;
NODE_ACCESS_WRITE_EXCLUSIVE ::  	(0x8) ;
NODE_FILE :: 			(0);
NODE_DIRECTORY :: 		(0x10);
NODE_INVALID :: 			(0x20);
NODE_FAIL_IF_FOUND :: 		(0x1000);
NODE_FAIL_IF_NOT_FOUND :: 	(0x2000);
NODE_PREVENT_RESIZE :: 		(0x4000);
NODE_CREATE_DIRECTORIES :: 	(0x8000)  ;
NODE_POSIX_NAMESPACE :: 		(0x10000) ;
_ES_NODE_FROM_WRITE_EXCLUSIVE :: 	(0x20000);
DIRECTORY_CHILDREN_UNKNOWN :: 	((  FileOffsetDifference) (-1));
MEMORY_OPEN_FAIL_IF_FOUND ::      (0x1000);
MEMORY_OPEN_FAIL_IF_NOT_FOUND ::  (0x2000);
MAP_OBJECT_READ_WRITE ::          (0);
MAP_OBJECT_READ_ONLY ::           (1);
MAP_OBJECT_COPY_ON_WRITE ::       (2);
STRING_FORMAT_ENOUGH_SPACE :: 	(                  (-1));
STRING_FORMAT_SIMPLE :: 		(1 << 0);
POSIX_SYSCALL_GET_POSIX_FD_PATH ::  	(0x10000);
PERMISSION_ACCESS_SYSTEM_FILES ::   	(1 << 0);
PERMISSION_ACCESS_USER_FILES ::   	(1 << 1);
PERMISSION_PROCESS_CREATE :: 		(1 << 2);
PERMISSION_PROCESS_OPEN :: 		(1 << 3);
PERMISSION_SCREEN_MODIFY :: 		(1 << 4)	;
PERMISSION_SHUTDOWN :: 			(1 << 5);
PERMISSION_TAKE_SYSTEM_SNAPSHOT :: 	(1 << 6);
PERMISSION_WINDOW_OPEN :: 		(1 << 7);
PERMISSION_ALL :: 			(                  (-1));
PERMISSION_INHERIT :: 			(                  (1) << 63);
PANEL_STYLE_DEFAULT :: 			"Panel.Default";
PANEL_STYLE_TRANSPARENT :: 		"Panel.Transparent";
PANEL_STYLE_WINDOW_BACKGROUND :: 		"Panel.WindowBackground";
PANEL_STYLE_WINDOW_DIVIDER :: 		"Panel.WindowDivider";
PANEL_STYLE_SHEET :: 			"Panel.Sheet";
PANEL_STYLE_GROUP_BOX :: 			"Panel.GroupBox";
PANEL_STYLE_INDENT :: 			"Panel.Indent";
PANEL_BAND_SIZE_DEFAULT ::               (-1);
ELEMENT_FOCUSABLE :: 			(                  (1) << 32);
ELEMENT_HIDDEN :: 			(                  (1) << 33) ;
ELEMENT_DISABLED :: 			(                  (1) << 34) ;
ELEMENT_DEBUG :: 				(                  (1) << 35) ;
ELEMENT_SCROLL_X :: 			(                  (1) << 36);
ELEMENT_SCROLL_Y :: 			(                  (1) << 37);
ELEMENT_NO_HOVER :: 			(                  (1) << 38) ;
ELEMENT_BLOCK_FOCUS :: 			(                  (1) << 39) ;
ELEMENT_NOT_TAB_TRAVERSABLE :: 		(                  (1) << 40) ;
ELEMENT_NO_INFORM_PARENT :: 		(                  (1) << 41) ;
ELEMENT_CENTER_ACCESS_KEY_HINT :: 	(                  (1) << 42) ;
ELEMENT_LAYOUT_HINT_HORIZONTAL :: 	(                  (1) << 43) ;
ELEMENT_STICKY_ACCESS_KEY :: 		(                  (1) << 44) ;
CELL_NEW_BAND :: 		(                  (1) << 51);
CELL_COLLAPSABLE :: 	(                  (1) << 51);
CELL_H_PUSH ::            (                  (1) << 54);
CELL_H_EXPAND ::          (                  (1) << 55);
CELL_H_SHRINK :: 		(                  (1) << 56);
CELL_H_LEFT ::            (                  (1) << 57);
CELL_H_RIGHT ::           (                  (1) << 58);
CELL_V_PUSH ::            (                  (1) << 59);
CELL_V_EXPAND ::          (                  (1) << 60);
CELL_V_SHRINK :: 		(                  (1) << 61);
CELL_V_TOP ::             (                  (1) << 62);
CELL_V_BOTTOM ::          (                  (1) << 63);
PANEL_VERTICAL :: 	(0x0001);
PANEL_WRAP :: 		(0x0002);
PANEL_TABLE :: 		(0x0004);
PANEL_Z_STACK :: 		(0x0008);
PANEL_H_LEFT :: 		(0x0010);
PANEL_H_RIGHT :: 		(0x0020);
PANEL_H_CENTER :: 	(0x0040);
PANEL_H_JUSTIFY :: 	(0x0080);
PANEL_V_TOP :: 		(0x0100);
PANEL_V_BOTTOM :: 	(0x0200);
PANEL_V_CENTER :: 	(0x0400);
PANEL_V_JUSTIFY :: 	(0x0800);
PANEL_H_SCROLL :: 	(0x1000);
PANEL_V_SCROLL :: 	(0x2000);
PANEL_SWITCHER :: 	(0x4000);
TEXTBOX_MULTILINE :: 			(1 << 0);
TEXTBOX_EDIT_BASED :: 			(1 << 1);
TEXTBOX_COMPACT :: 			(1 << 2);
TEXTBOX_NO_SMART_CONTEXT_MENUS :: 	(1 << 3);
TEXTBOX_FIND_BACKWARDS :: 		(1 << 0);
BUTTON_DEFAULT :: 			(1 << 0);
BUTTON_DANGEROUS :: 			(1 << 1);
BUTTON_MENU_ITEM :: 			(1 << 2);
BUTTON_NOT_FOCUSABLE :: 			(1 << 3);
BUTTON_TOOLBAR :: 			(1 << 4);
BUTTON_DROPDOWN :: 			(1 << 5);
BUTTON_COMPACT :: 			(1 << 6);
MENU_ITEM_HEADER :: 			(1 << 7);
BUTTON_CHECKBOX :: 			(1 << 8);
BUTTON_RADIOBOX :: 			(1 << 9);
BUTTON_CANCEL :: 				(1 << 10);
BUTTON_PUSH :: 				(1 << 11);
COLOR_WELL_HAS_OPACITY :: 		(1 << 0);
SCROLLBAR_VERTICAL :: 			(0 << 0);
SCROLLBAR_HORIZONTAL :: 			(1 << 0);
SPLITTER_VERTICAL :: 			(0 << 0);
SPLITTER_HORIZONTAL :: 			(1 << 0);
IMAGE_DISPLAY_LOAD_ASYNCHRONOUSLY :: 	(1 << 0);
IMAGE_DISPLAY_UNLOAD_WHEN_HIDDEN :: 	(1 << 1);
IMAGE_DISPLAY_UPSCALE_NEAREST :: 		(1 << 2);
IMAGE_DISPLAY_DOWNSCALE_NEAREST :: 	(1 << 3);
IMAGE_DISPLAY_OPAQUE :: 			(1 << 4);
LIST_VIEW_HORIZONTAL :: 			(1 << 0) ;
LIST_VIEW_VARIABLE_SIZE :: 		(1 << 1) ;
LIST_VIEW_TILED :: 			(1 << 2) ;
LIST_VIEW_NON_LINEAR :: 			(1 << 3) ;
LIST_VIEW_SINGLE_SELECT :: 		(1 << 4) ;
LIST_VIEW_MULTI_SELECT :: 		(1 << 5) ;
LIST_VIEW_COLUMNS :: 			(1 << 6) ;
LIST_VIEW_GROUP_HAS_HEADER :: 		(1 << 0) ;
LIST_VIEW_GROUP_HAS_FOOTER :: 		(1 << 1) ;
LIST_VIEW_GROUP_INDENT :: 		(1 << 2) ;
LIST_VIEW_GROUP_COLLAPSABLE :: 		(1 << 3) ;
LIST_VIEW_COLUMN_RIGHT_ALIGNED :: 	(1 << 0) ;
LIST_VIEW_COLUMN_ASCENDING :: 		(1 << 1) ;
LIST_VIEW_COLUMN_DESCENDING :: 		(1 << 2) ;
LIST_VIEW_COLUMN_HAS_MENU :: 		(1 << 3) ;
MENU_AT_CURSOR :: 			(1 << 0);
MENU_MAXIMUM_HEIGHT :: 			(1 << 1);
FONT_SANS ::  		(0xFFFF);
FONT_SERIF ::  		(0xFFFE);
FONT_MONOSPACED ::  	(0xFFFD);
FONT_REGULAR :: 		(4);
FONT_BOLD ::  		(7);
TEXT_FIGURE_DEFAULT :: 	(0);
TEXT_FIGURE_OLD :: 	(1);
TEXT_FIGURE_TABULAR :: 	(2);
TEXT_DISPLAY_CONTENT_MAX_VALUES ::  (8);
DIRECTORY_MONITOR_SUBTREE ::  	(1 << 0) ;
DIRECTORY_MONITOR_CONTENTS :: 	(1 << 1) ;
DIRECTORY_MONITOR_MODIFY :: 	(1 << 2) ;
COMMAND_SYSTEM_START :: 		(0xF0000000);
COMMAND_DELETE :: 		(0xF0000001);
COMMAND_SELECT_ALL :: 		(0xF0000002);
COMMAND_CUT :: 			(0xF0000003);
COMMAND_COPY :: 			(0xF0000004);
COMMAND_PASTE :: 			(0xF0000005);
AUDIO_STREAM_OVERRUN ::         (1 << 0);
AUDIO_STREAM_UNDERRUN ::        (1 << 0);
AUDIO_STREAM_FORMAT_CHANGED ::  (1 << 1);
AUDIO_STREAM_TERMINATED ::      (1 << 2);
AUDIO_STREAM_RUNNING ::         (1 << 0);
AUDIO_STREAM_MUTED ::           (1 << 1);
AUDIO_STREAM_ONESHOT ::         (1 << 2);
AUDIO_DEFAULT_OUTPUT ::  (1);
SAMPLE_FORMAT_U8 ::     (1);
SAMPLE_FORMAT_S16LE ::  (2);
SAMPLE_FORMAT_S32LE ::  (3);
SAMPLE_FORMAT_F32LE ::  (4) ;
CELL_FILL :: 	 (   CELL_H_FILL |    CELL_V_FILL);
CELL_H_FILL :: 	 (   CELL_H_PUSH |    CELL_H_EXPAND |    CELL_H_SHRINK);
CELL_V_FILL :: 	 (   CELL_V_PUSH |    CELL_V_EXPAND |    CELL_V_SHRINK);
CELL_CENTER :: 	 (   CELL_H_CENTER |    CELL_V_CENTER);
CELL_PUSH :: 	 (   CELL_H_PUSH |    CELL_V_PUSH);
CELL_EXPAND ::     (   CELL_H_EXPAND |    CELL_V_EXPAND);
CELL_CORNER :: 	 (   CELL_H_LEFT |    CELL_V_TOP);
CELL_SHRINK :: 	 (   CELL_H_SHRINK |    CELL_V_SHRINK);
CELL_H_CENTER ::   (   CELL_H_LEFT |    CELL_H_RIGHT);
CELL_V_CENTER ::   (   CELL_V_TOP |    CELL_V_BOTTOM);
THEME_METRICS_INSETS ::  (1 << 0);
THEME_METRICS_CLIP_INSETS ::  (1 << 1);
THEME_METRICS_GLOBAL_OFFSET ::  (1 << 2)	;
THEME_METRICS_CLIP_ENABLED ::  (1 << 3);
THEME_METRICS_CURSOR ::  (1 << 4);
THEME_METRICS_ENTRANCE_TRANSITION ::  (1 << 5);
THEME_METRICS_EXIT_TRANSITION ::  (1 << 6);
THEME_METRICS_ENTRANCE_DURATION ::  (1 << 7);
THEME_METRICS_EXIT_DURATION ::  (1 << 8);
THEME_METRICS_PREFERRED_WIDTH ::  (1 << 9);
THEME_METRICS_PREFERRED_HEIGHT ::  (1 << 10);
THEME_METRICS_MINIMUM_WIDTH ::  (1 << 11);
THEME_METRICS_MINIMUM_HEIGHT ::  (1 << 12);
THEME_METRICS_MAXIMUM_WIDTH ::  (1 << 13);
THEME_METRICS_MAXIMUM_HEIGHT ::  (1 << 14);
THEME_METRICS_GAP_MAJOR ::  (1 << 15);
THEME_METRICS_GAP_MINOR ::  (1 << 16);
THEME_METRICS_GAP_WRAP ::  (1 << 17);
THEME_METRICS_GAP_ALL ::  (   THEME_METRICS_GAP_MAJOR |    THEME_METRICS_GAP_MINOR |    THEME_METRICS_GAP_WRAP);
THEME_METRICS_TEXT_COLOR ::  (1 << 18);
THEME_METRICS_SELECTED_BACKGROUND ::  (1 << 19);
THEME_METRICS_SELECTED_TEXT ::  (1 << 20);
THEME_METRICS_ICON_COLOR ::  (1 << 21);
THEME_METRICS_TEXT_ALIGN ::  (1 << 22);
THEME_METRICS_TEXT_SIZE ::  (1 << 23);
THEME_METRICS_FONT_FAMILY ::  (1 << 24);
THEME_METRICS_FONT_WEIGHT ::  (1 << 25);
THEME_METRICS_ICON_SIZE ::  (1 << 26);
THEME_METRICS_IS_ITALIC ::  (1 << 27);
THEME_METRICS_ELLIPSIS ::  (1 << 28);
THEME_METRICS_WRAP_TEXT ::  (1 << 29);
MOVE_WINDOW_MAXIMISED ::  (1 << 0);
MOVE_WINDOW_ADJUST_TO_FIT_SCREEN ::  (1 << 1);
MOVE_WINDOW_HIDDEN ::  (1 << 2);
MOVE_WINDOW_ALWAYS_ON_TOP ::  (1 << 3);
MOVE_WINDOW_AT_BOTTOM ::  (1 << 4);
MOVE_WINDOW_UPDATE_SCREEN ::  (1 << 5);
WINDOW_SOLID_TRUE ::  (1 << 0);
WINDOW_SOLID_NO_ACTIVATE ::  (1 << 1);
THEME_BITMAP_WIDTH ::  (400);
THEME_BITMAP_HEIGHT ::  (200);
THEME_BITMAP_NAME ::  "Desktop.ThemeBitmap";
TEXTBOX_MOVE_CARET_SINGLE ::  (2);
TEXTBOX_MOVE_CARET_WORD ::  (3);
TEXTBOX_MOVE_CARET_LINE ::  (4);
TEXTBOX_MOVE_CARET_VERTICAL ::  (5);
TEXTBOX_MOVE_CARET_ALL ::  (6);
TEXTBOX_MOVE_CARET_FIRST_ONLY ::  (1 << 8);
TEXTBOX_MOVE_CARET_SECOND_ONLY ::  (1 << 9);
TEXTBOX_MOVE_CARET_BACKWARDS ::  (1 << 10);
TEXTBOX_MOVE_CARET_STRONG_WHITESPACE ::  (1 << 11);
GAME_CONTROLLER_MAX_COUNT ::  (16);
DOMAIN_NAME_MAX_LENGTH ::  (255);
ECHO_REQUEST_MAX_LENGTH ::  (48);
CONNECTION_OPEN_WAIT ::  (1 << 0);
FILE_CONTROL_NOTIFY_MONITORS ::  (1 << 0);
FILE_CONTROL_FLUSH ::  (1 << 1);
using StandardIcon :: enum i32 {
	ICON_NONE,
	ICON_ACTION_UNAVAILABLE_SYMBOLIC,
	ICON_ADDRESS_BOOK_NEW,
	ICON_ADDRESS_BOOK_NEW_SYMBOLIC,
	ICON_ALIGN_HORIZONTAL_CENTER,
	ICON_ALIGN_HORIZONTAL_CENTER_SYMBOLIC,
	ICON_ALIGN_HORIZONTAL_LEFT,
	ICON_ALIGN_HORIZONTAL_LEFT_SYMBOLIC,
	ICON_ALIGN_HORIZONTAL_LEFT_TO_ANCHOR,
	ICON_ALIGN_HORIZONTAL_LEFT_TO_ANCHOR_SYMBOLIC,
	ICON_ALIGN_HORIZONTAL_RIGHT,
	ICON_ALIGN_HORIZONTAL_RIGHT_SYMBOLIC,
	ICON_ALIGN_HORIZONTAL_RIGHT_TO_ANCHOR,
	ICON_ALIGN_HORIZONTAL_RIGHT_TO_ANCHOR_SYMBOLIC,
	ICON_ALIGN_VERTICAL_BOTTOM,
	ICON_ALIGN_VERTICAL_BOTTOM_SYMBOLIC,
	ICON_ALIGN_VERTICAL_BOTTOM_TO_ANCHOR,
	ICON_ALIGN_VERTICAL_BOTTOM_TO_ANCHOR_SYMBOLIC,
	ICON_ALIGN_VERTICAL_CENTER,
	ICON_ALIGN_VERTICAL_CENTER_SYMBOLIC,
	ICON_ALIGN_VERTICAL_TOP,
	ICON_ALIGN_VERTICAL_TOP_SYMBOLIC,
	ICON_ALIGN_VERTICAL_TOP_TO_ANCHOR,
	ICON_ALIGN_VERTICAL_TOP_TO_ANCHOR_SYMBOLIC,
	ICON_APPLICATION_ADD_SYMBOLIC,
	ICON_APPOINTMENT_NEW,
	ICON_APPOINTMENT_NEW_SYMBOLIC,
	ICON_APPOINTMENT_SYMBOLIC,
	ICON_BOOKMARK_NEW,
	ICON_BOOKMARK_NEW_SYMBOLIC,
	ICON_BROWSER_DOWNLOAD,
	ICON_BROWSER_DOWNLOAD_SYMBOLIC,
	ICON_CALL_START,
	ICON_CALL_START_SYMBOLIC,
	ICON_CALL_STOP,
	ICON_CALL_STOP_SYMBOLIC,
	ICON_COLOR_FILL,
	ICON_COLOR_GRADIENT,
	ICON_COLOR_GRADIENT_MESH,
	ICON_COLOR_SELECT_SYMBOLIC,
	ICON_CONTACT_NEW,
	ICON_CONTACT_NEW_SYMBOLIC,
	ICON_DISTRIBUTE_HORIZONTAL_CENTER,
	ICON_DISTRIBUTE_HORIZONTAL_GAPS,
	ICON_DISTRIBUTE_HORIZONTAL_LEFT,
	ICON_DISTRIBUTE_HORIZONTAL_RIGHT,
	ICON_DISTRIBUTE_VERTICAL_BOTTOM,
	ICON_DISTRIBUTE_VERTICAL_CENTER,
	ICON_DISTRIBUTE_VERTICAL_GAPS,
	ICON_DISTRIBUTE_VERTICAL_TOP,
	ICON_DOCUMENT_EDIT,
	ICON_DOCUMENT_EDIT_SYMBOLIC,
	ICON_DOCUMENT_EXPORT,
	ICON_DOCUMENT_EXPORT_SYMBOLIC,
	ICON_DOCUMENT_IMPORT,
	ICON_DOCUMENT_IMPORT_SYMBOLIC,
	ICON_DOCUMENT_NEW,
	ICON_DOCUMENT_NEW_SYMBOLIC,
	ICON_DOCUMENT_OPEN_RECENT,
	ICON_DOCUMENT_OPEN_RECENT_SYMBOLIC,
	ICON_DOCUMENT_OPEN_SYMBOLIC,
	ICON_DOCUMENT_PAGE_SETUP,
	ICON_DOCUMENT_PAGE_SETUP_SYMBOLIC,
	ICON_DOCUMENT_PRINT_PREVIEW,
	ICON_DOCUMENT_PRINT_PREVIEW_SYMBOLIC,
	ICON_DOCUMENT_PRINT_SYMBOLIC,
	ICON_DOCUMENT_PROPERTIES,
	ICON_DOCUMENT_PROPERTIES_SYMBOLIC,
	ICON_DOCUMENT_REVERT,
	ICON_DOCUMENT_REVERT_SYMBOLIC,
	ICON_DOCUMENT_SAVE_AS,
	ICON_DOCUMENT_SAVE_AS_SYMBOLIC,
	ICON_DOCUMENT_SAVE_SYMBOLIC,
	ICON_DOCUMENT_SEND,
	ICON_DOCUMENT_SEND_SYMBOLIC,
	ICON_DRAW_CUBOID,
	ICON_DRAW_ELLIPSE,
	ICON_DRAW_ERASER,
	ICON_DRAW_FREEHAND,
	ICON_DRAW_PATH,
	ICON_DRAW_POLYGON_STAR,
	ICON_DRAW_RECTANGLE,
	ICON_DRAW_SPIRAL,
	ICON_DRAW_TEXT,
	ICON_EDIT_CLEAR,
	ICON_EDIT_CLEAR_ALL_SYMBOLIC,
	ICON_EDIT_CLEAR_SYMBOLIC,
	ICON_EDIT_COPY,
	ICON_EDIT_COPY_SYMBOLIC,
	ICON_EDIT_CUT,
	ICON_EDIT_CUT_SYMBOLIC,
	ICON_EDIT_DELETE_SYMBOLIC,
	ICON_EDIT_FIND,
	ICON_EDIT_FIND_REPLACE,
	ICON_EDIT_FIND_REPLACE_SYMBOLIC,
	ICON_EDIT_FIND_SYMBOLIC,
	ICON_EDIT_FLAG,
	ICON_EDIT_FLAG_SYMBOLIC,
	ICON_EDIT_MARK,
	ICON_EDIT_PASTE,
	ICON_EDIT_PASTE_SYMBOLIC,
	ICON_EDIT_REDO,
	ICON_EDIT_REDO_SYMBOLIC,
	ICON_EDIT_SELECT_ALL,
	ICON_EDIT_SELECT_ALL_SYMBOLIC,
	ICON_EDIT_SELECT_SYMBOLIC,
	ICON_EDIT_UNDO,
	ICON_EDIT_UNDO_ARCHIVE,
	ICON_EDIT_UNDO_SYMBOLIC,
	ICON_ERROR_CORRECT_SYMBOLIC,
	ICON_EVENT_NEW,
	ICON_FIND_LOCATION,
	ICON_FIND_LOCATION_SYMBOLIC,
	ICON_FOLDER_COPY,
	ICON_FOLDER_MOVE,
	ICON_FOLDER_NEW,
	ICON_FOLDER_NEW_SYMBOLIC,
	ICON_FONT_SELECT_SYMBOLIC,
	ICON_FORMAT_INDENT_LESS,
	ICON_FORMAT_INDENT_LESS_SYMBOLIC,
	ICON_FORMAT_INDENT_MORE,
	ICON_FORMAT_INDENT_MORE_SYMBOLIC,
	ICON_FORMAT_JUSTIFY_CENTER,
	ICON_FORMAT_JUSTIFY_CENTER_SYMBOLIC,
	ICON_FORMAT_JUSTIFY_FILL,
	ICON_FORMAT_JUSTIFY_FILL_SYMBOLIC,
	ICON_FORMAT_JUSTIFY_LEFT,
	ICON_FORMAT_JUSTIFY_LEFT_SYMBOLIC,
	ICON_FORMAT_JUSTIFY_RIGHT,
	ICON_FORMAT_JUSTIFY_RIGHT_SYMBOLIC,
	ICON_FORMAT_TEXT_BOLD,
	ICON_FORMAT_TEXT_BOLD_ES_SYMBOLIC,
	ICON_FORMAT_TEXT_BOLD_FR_SYMBOLIC,
	ICON_FORMAT_TEXT_BOLD_SYMBOLIC,
	ICON_FORMAT_TEXT_CLEAR_FORMATTING_SYMBOLIC,
	ICON_FORMAT_TEXT_DIRECTION_LTR_SYMBOLIC,
	ICON_FORMAT_TEXT_HIGHLIGHT,
	ICON_FORMAT_TEXT_ITALIC,
	ICON_FORMAT_TEXT_ITALIC_ES_SYMBOLIC,
	ICON_FORMAT_TEXT_ITALIC_SYMBOLIC,
	ICON_FORMAT_TEXT_LARGER_SYMBOLIC,
	ICON_FORMAT_TEXT_NONE,
	ICON_FORMAT_TEXT_SMALLER_SYMBOLIC,
	ICON_FORMAT_TEXT_STRIKETHROUGH,
	ICON_FORMAT_TEXT_STRIKETHROUGH_FR_SYMBOLIC,
	ICON_FORMAT_TEXT_STRIKETHROUGH_SYMBOLIC,
	ICON_FORMAT_TEXT_UNDERLINE,
	ICON_FORMAT_TEXT_UNDERLINE_FR_SYMBOLIC,
	ICON_FORMAT_TEXT_UNDERLINE_SYMBOLIC,
	ICON_GO_BOTTOM,
	ICON_GO_BOTTOM_SYMBOLIC,
	ICON_GO_DOWN,
	ICON_GO_DOWN_SYMBOLIC,
	ICON_GO_FIRST,
	ICON_GO_FIRST_SYMBOLIC,
	ICON_GO_HOME_SYMBOLIC,
	ICON_GO_JUMP,
	ICON_GO_JUMP_SYMBOLIC,
	ICON_GO_LAST,
	ICON_GO_LAST_SYMBOLIC,
	ICON_GO_NEXT,
	ICON_GO_NEXT_SYMBOLIC,
	ICON_GO_PREVIOUS,
	ICON_GO_PREVIOUS_SYMBOLIC,
	ICON_GO_TOP,
	ICON_GO_TOP_SYMBOLIC,
	ICON_GO_UP,
	ICON_GO_UP_SYMBOLIC,
	ICON_HELP_ABOUT,
	ICON_HELP_ABOUT_SYMBOLIC,
	ICON_HELP_CONTENTS,
	ICON_HELP_CONTENTS_SYMBOLIC,
	ICON_HELP_INFO_SYMBOLIC,
	ICON_IMAGE_ADJUST,
	ICON_IMAGE_AUTO_ADJUST,
	ICON_IMAGE_CROP,
	ICON_IMAGE_CROP_SYMBOLIC,
	ICON_IMAGE_RED_EYE,
	ICON_IMAGE_RED_EYE_SYMBOLIC,
	ICON_INSERT_IMAGE,
	ICON_INSERT_IMAGE_SYMBOLIC,
	ICON_INSERT_LINK,
	ICON_INSERT_LINK_SYMBOLIC,
	ICON_INSERT_OBJECT,
	ICON_INSERT_OBJECT_SYMBOLIC,
	ICON_INSERT_TEXT_SYMBOLIC,
	ICON_LIST_ADD,
	ICON_LIST_ADD_SYMBOLIC,
	ICON_LIST_REMOVE,
	ICON_LIST_REMOVE_SYMBOLIC,
	ICON_MAIL_ARCHIVE,
	ICON_MAIL_FORWARD,
	ICON_MAIL_FORWARD_SYMBOLIC,
	ICON_MAIL_MARK_IMPORTANT,
	ICON_MAIL_MARK_IMPORTANT_SYMBOLIC,
	ICON_MAIL_MARK_JUNK,
	ICON_MAIL_MARK_JUNK_SYMBOLIC,
	ICON_MAIL_MARK_NOTJUNK,
	ICON_MAIL_MARK_NOTJUNK_SYMBOLIC,
	ICON_MAIL_MESSAGE_NEW,
	ICON_MAIL_MESSAGE_NEW_SYMBOLIC,
	ICON_MAIL_MOVE,
	ICON_MAIL_MOVE_SYMBOLIC,
	ICON_MAIL_REPLY_ALL,
	ICON_MAIL_REPLY_ALL_SYMBOLIC,
	ICON_MAIL_REPLY_SENDER,
	ICON_MAIL_REPLY_SENDER_SYMBOLIC,
	ICON_MAIL_SEND,
	ICON_MAIL_SEND_RECEIVE_SYMBOLIC,
	ICON_MAIL_SEND_SYMBOLIC,
	ICON_MARK_LOCATION_SYMBOLIC,
	ICON_MEDIA_EJECT,
	ICON_MEDIA_EJECT_SYMBOLIC,
	ICON_MEDIA_EQ_SYMBOLIC,
	ICON_MEDIA_PLAYBACK_PAUSE,
	ICON_MEDIA_PLAYBACK_PAUSE_SYMBOLIC,
	ICON_MEDIA_PLAYBACK_START,
	ICON_MEDIA_PLAYBACK_START_SYMBOLIC,
	ICON_MEDIA_PLAYBACK_STOP,
	ICON_MEDIA_PLAYBACK_STOP_SYMBOLIC,
	ICON_MEDIA_RECORD,
	ICON_MEDIA_RECORD_SYMBOLIC,
	ICON_MEDIA_SEEK_BACKWARD,
	ICON_MEDIA_SEEK_BACKWARD_SYMBOLIC,
	ICON_MEDIA_SEEK_FORWARD,
	ICON_MEDIA_SEEK_FORWARD_SYMBOLIC,
	ICON_MEDIA_SKIP_BACKWARD,
	ICON_MEDIA_SKIP_FORWARD,
	ICON_MEDIA_VIEW_SUBTITLES_SYMBOLIC,
	ICON_NODE_ADD,
	ICON_NODE_ALIGN_HORIZONTAL,
	ICON_NODE_ALIGN_VERTICAL,
	ICON_NODE_BREAK,
	ICON_NODE_CUSP,
	ICON_NODE_DELETE,
	ICON_NODE_DELETE_SEGMENT,
	ICON_NODE_DISTRIBUTE_HORIZONTAL,
	ICON_NODE_DISTRIBUTE_VERTICAL,
	ICON_NODE_INSERT,
	ICON_NODE_JOIN,
	ICON_NODE_JOIN_SEGMENT,
	ICON_NODE_SMOOTH,
	ICON_NODE_SYMMETRIC,
	ICON_OBJECT_FLIP_HORIZONTAL,
	ICON_OBJECT_FLIP_HORIZONTAL_SYMBOLIC,
	ICON_OBJECT_FLIP_VERTICAL,
	ICON_OBJECT_FLIP_VERTICAL_SYMBOLIC,
	ICON_OBJECT_GROUP,
	ICON_OBJECT_GROUP_SYMBOLIC,
	ICON_OBJECT_INVERSE,
	ICON_OBJECT_INVERSE_SYMBOLIC,
	ICON_OBJECT_MERGE,
	ICON_OBJECT_ROTATE_LEFT,
	ICON_OBJECT_ROTATE_LEFT_SYMBOLIC,
	ICON_OBJECT_ROTATE_RIGHT,
	ICON_OBJECT_ROTATE_RIGHT_SYMBOLIC,
	ICON_OBJECT_SELECT_SYMBOLIC,
	ICON_OBJECT_STRAIGHTEN,
	ICON_OBJECT_TO_PATH,
	ICON_OBJECT_UNGROUP,
	ICON_OBJECT_UNGROUP_SYMBOLIC,
	ICON_OPEN_MENU,
	ICON_OPEN_MENU_SYMBOLIC,
	ICON_PAN_DOWN_SYMBOLIC,
	ICON_PANE_HIDE_SYMBOLIC,
	ICON_PAN_END_SYMBOLIC,
	ICON_PANE_SHOW_SYMBOLIC,
	ICON_PAN_START_SYMBOLIC,
	ICON_PAN_UP_SYMBOLIC,
	ICON_PATH_BREAK_APART,
	ICON_PATH_BREAK_APART_SYMBOLIC,
	ICON_PATH_COMBINE,
	ICON_PATH_COMBINE_SYMBOLIC,
	ICON_PATH_DIFFERENCE,
	ICON_PATH_DIFFERENCE_SYMBOLIC,
	ICON_PATH_DIVISION,
	ICON_PATH_DIVISION_SYMBOLIC,
	ICON_PATH_EXCLUSION,
	ICON_PATH_EXCLUSION_SYMBOLIC,
	ICON_PATH_INTERSECTION,
	ICON_PATH_INTERSECTION_SYMBOLIC,
	ICON_PATH_UNION,
	ICON_PATH_UNION_SYMBOLIC,
	ICON_PROCESS_STOP,
	ICON_PROCESS_STOP_SYMBOLIC,
	ICON_SEGMENT_CURVE,
	ICON_SEGMENT_LINE,
	ICON_SELECTION_ADD,
	ICON_SELECTION_BOTTOM,
	ICON_SELECTION_BOTTOM_SYMBOLIC,
	ICON_SELECTION_CHECKED,
	ICON_SELECTION_END_SYMBOLIC,
	ICON_SELECTION_LOWER,
	ICON_SELECTION_LOWER_SYMBOLIC,
	ICON_SELECTION_RAISE,
	ICON_SELECTION_RAISE_SYMBOLIC,
	ICON_SELECTION_REMOVE,
	ICON_SELECTION_START_SYMBOLIC,
	ICON_SELECTION_TOP,
	ICON_SELECTION_TOP_SYMBOLIC,
	ICON_SEND_TO,
	ICON_SEND_TO_SYMBOLIC,
	ICON_STAR_NEW_SYMBOLIC,
	ICON_STROKE_TO_PATH,
	ICON_SYSTEM_LOCK_SCREEN,
	ICON_SYSTEM_LOCK_SCREEN_SYMBOLIC,
	ICON_SYSTEM_LOG_OUT,
	ICON_SYSTEM_REBOOT,
	ICON_SYSTEM_RUN,
	ICON_SYSTEM_RUN_SYMBOLIC,
	ICON_SYSTEM_SHUTDOWN,
	ICON_SYSTEM_SHUTDOWN_SYMBOLIC,
	ICON_SYSTEM_SUSPEND,
	ICON_TAB_NEW_SYMBOLIC,
	ICON_TAG_NEW,
	ICON_TAG_NEW_SYMBOLIC,
	ICON_TOOL_MEASURE,
	ICON_TOOL_NODE_EDITOR,
	ICON_TOOLS_CHECK_SPELLING_SYMBOLIC,
	ICON_TOOLS_TIMER_SYMBOLIC,
	ICON_VIEW_COLUMN_SYMBOLIC,
	ICON_VIEW_CONTINUOUS_SYMBOLIC,
	ICON_VIEW_DUAL_SYMBOLIC,
	ICON_VIEW_FILTER_SYMBOLIC,
	ICON_VIEW_FULLSCREEN_SYMBOLIC,
	ICON_VIEW_GRID_SYMBOLIC,
	ICON_VIEW_LIST_COMPACT_SYMBOLIC,
	ICON_VIEW_LIST_IMAGES_SYMBOLIC,
	ICON_VIEW_LIST_SYMBOLIC,
	ICON_VIEW_LIST_VIDEO_SYMBOLIC,
	ICON_VIEW_MORE_HORIZONTAL_SYMBOLIC,
	ICON_VIEW_MORE_SYMBOLIC,
	ICON_VIEW_PAGED_SYMBOLIC,
	ICON_VIEW_PIN_SYMBOLIC,
	ICON_VIEW_READER,
	ICON_VIEW_READER_SYMBOLIC,
	ICON_VIEW_REFRESH,
	ICON_VIEW_REFRESH_SYMBOLIC,
	ICON_VIEW_RESTORE_SYMBOLIC,
	ICON_VIEW_SORT_ASCENDING_SYMBOLIC,
	ICON_VIEW_SORT_DESCENDING_SYMBOLIC,
	ICON_WINDOW_CLOSE,
	ICON_WINDOW_CLOSE_SYMBOLIC,
	ICON_WINDOW_MAXIMIZE_SYMBOLIC,
	ICON_WINDOW_MINIMIZE_SYMBOLIC,
	ICON_WINDOW_NEW,
	ICON_WINDOW_NEW_SYMBOLIC,
	ICON_WINDOW_POP_OUT_SYMBOLIC,
	ICON_WINDOW_RESTORE_SYMBOLIC,
	ICON_ZOOM_FIT_BEST,
	ICON_ZOOM_FIT_BEST_SYMBOLIC,
	ICON_ZOOM_IN,
	ICON_ZOOM_IN_SYMBOLIC,
	ICON_ZOOM_ORIGINAL,
	ICON_ZOOM_ORIGINAL_SYMBOLIC,
	ICON_ZOOM_OUT,
	ICON_ZOOM_OUT_SYMBOLIC,
	ICON_ACCESSORIES_CALCULATOR,
	ICON_ACCESSORIES_CALCULATOR_SYMBOLIC,
	ICON_ACCESSORIES_SCREENSHOT,
	ICON_ACCESSORIES_TEXT_EDITOR,
	ICON_ACCESSORIES_TEXT_EDITOR_SYMBOLIC,
	ICON_APPLICATION_DEFAULT_ICON,
	ICON_ARCHIVE_MANAGER,
	ICON_INTERNET_CHAT,
	ICON_INTERNET_CHAT_SYMBOLIC,
	ICON_INTERNET_MAIL,
	ICON_INTERNET_MAIL_SYMBOLIC,
	ICON_INTERNET_NEWS_READER,
	ICON_INTERNET_NEWS_READER_SYMBOLIC,
	ICON_INTERNET_WEB_BROWSER,
	ICON_INTERNET_WEB_BROWSER_SYMBOLIC,
	ICON_MULTIMEDIA_AUDIO_PLAYER,
	ICON_MULTIMEDIA_PHOTO_MANAGER,
	ICON_MULTIMEDIA_VIDEO_PLAYER,
	ICON_OFFICE_ADDRESS_BOOK,
	ICON_OFFICE_CALENDAR,
	ICON_OFFICE_CALENDAR_SYMBOLIC,
	ICON_ONBOARD,
	ICON_POSTSCRIPT_VIEWER,
	ICON_PREFERENCES_DESKTOP,
	ICON_PREFERENCES_DESKTOP_FONT,
	ICON_SYSTEM_FILE_MANAGER,
	ICON_SYSTEM_OS_INSTALLER,
	ICON_SYSTEM_SOFTWARE_INSTALL,
	ICON_SYSTEM_SOFTWARE_INSTALL_SYMBOLIC,
	ICON_SYSTEM_SOFTWARE_UPDATE,
	ICON_SYSTEM_SOFTWARE_UPDATE_SYMBOLIC,
	ICON_SYSTEM_USERS,
	ICON_SYSTEM_USERS_SYMBOLIC,
	ICON_UTILITIES_SYSTEM_MONITOR,
	ICON_UTILITIES_TERMINAL,
	ICON_UTILITIES_TERMINAL_SYMBOLIC,
	ICON_APPLICATIONS_ACCESSORIES,
	ICON_APPLICATIONS_AUDIO_SYMBOLIC,
	ICON_APPLICATIONS_DEVELOPMENT,
	ICON_APPLICATIONS_DEVELOPMENT_SYMBOLIC,
	ICON_APPLICATIONS_EDUCATION,
	ICON_APPLICATIONS_EDUCATION_SYMBOLIC,
	ICON_APPLICATIONS_ENGINEERING_SYMBOLIC,
	ICON_APPLICATIONS_FONTS,
	ICON_APPLICATIONS_GAMES,
	ICON_APPLICATIONS_GAMES_SYMBOLIC,
	ICON_APPLICATIONS_GRAPHICS,
	ICON_APPLICATIONS_GRAPHICS_SYMBOLIC,
	ICON_APPLICATIONS_INTERFACEDESIGN,
	ICON_APPLICATIONS_INTERNET_SYMBOLIC,
	ICON_APPLICATIONS_MULTIMEDIA,
	ICON_APPLICATIONS_MULTIMEDIA_SYMBOLIC,
	ICON_APPLICATIONS_OFFICE,
	ICON_APPLICATIONS_OFFICE_SYMBOLIC,
	ICON_APPLICATIONS_OTHER,
	ICON_APPLICATIONS_OTHER_SYMBOLIC,
	ICON_APPLICATIONS_PHOTOGRAPHY,
	ICON_APPLICATIONS_SCIENCE,
	ICON_APPLICATIONS_SCIENCE_SYMBOLIC,
	ICON_APPLICATIONS_UTILITIES,
	ICON_APPLICATIONS_UTILITIES_SYMBOLIC,
	ICON_APPLICATIONS_VIDEO_SYMBOLIC,
	ICON_BUG,
	ICON_BUG_SYMBOLIC,
	ICON_EMOJI_ACTIVITY_SYMBOLIC,
	ICON_EMOJI_BODY_SYMBOLIC,
	ICON_EMOJI_FOOD_SYMBOLIC,
	ICON_EMOJI_NATURE_SYMBOLIC,
	ICON_EMOJI_OBJECTS_SYMBOLIC,
	ICON_EMOJI_TRAVEL_SYMBOLIC,
	ICON_EVENT_BIRTHDAY_SYMBOLIC,
	ICON_PREFERENCES_BLUETOOTH_SYMBOLIC,
	ICON_PREFERENCES_COLOR,
	ICON_PREFERENCES_COLOR_SYMBOLIC,
	ICON_PREFERENCES_DESKTOP_ACCESSIBILITY,
	ICON_PREFERENCES_DESKTOP_ACCESSIBILITY_POINTING,
	ICON_PREFERENCES_DESKTOP_ACCESSIBILITY_SYMBOLIC,
	ICON_PREFERENCES_DESKTOP_ACCESSIBILITY_ZOOM,
	ICON_PREFERENCES_DESKTOP_APPLICATIONS,
	ICON_PREFERENCES_DESKTOP_DISPLAY,
	ICON_PREFERENCES_DESKTOP_DISPLAY_SYMBOLIC,
	ICON_PREFERENCES_DESKTOP_KEYBOARD,
	ICON_PREFERENCES_DESKTOP_KEYBOARD_SYMBOLIC,
	ICON_PREFERENCES_DESKTOP_LOCALE,
	ICON_PREFERENCES_DESKTOP_LOCALE_SYMBOLIC,
	ICON_PREFERENCES_DESKTOP_ONLINE_ACCOUNTS,
	ICON_PREFERENCES_DESKTOP_ONLINE_ACCOUNTS_SYMBOLIC,
	ICON_PREFERENCES_DESKTOP_PERIPHERALS,
	ICON_PREFERENCES_DESKTOP_SOUND,
	ICON_PREFERENCES_DESKTOP_WALLPAPER,
	ICON_PREFERENCES_DESKTOP_WORKSPACES,
	ICON_PREFERENCES_OTHER_SYMBOLIC,
	ICON_PREFERENCES_SYSTEM,
	ICON_PREFERENCES_SYSTEM_NETWORK,
	ICON_PREFERENCES_SYSTEM_NETWORK_SYMBOLIC,
	ICON_PREFERENCES_SYSTEM_NOTIFICATIONS,
	ICON_PREFERENCES_SYSTEM_PARENTAL_CONTROLS,
	ICON_PREFERENCES_SYSTEM_PARENTAL_CONTROL_SYMBOLIC,
	ICON_PREFERENCES_SYSTEM_POWER,
	ICON_PREFERENCES_SYSTEM_POWER_SYMBOLIC,
	ICON_PREFERENCES_SYSTEM_PRIVACY_HOUSEKEEPING,
	ICON_PREFERENCES_SYSTEM_SHARING,
	ICON_PREFERENCES_SYSTEM_SHARING_SYMBOLIC,
	ICON_PREFERENCES_SYSTEM_TIME,
	ICON_PREFERENCES_SYSTEM_TIME_SYMBOLIC,
	ICON_PREFERENCES_SYSTEM_WINDOWS,
	ICON_AC_ADAPTER_SYMBOLIC,
	ICON_AUDIO_CARD_SYMBOLIC,
	ICON_AUDIO_HEADPHONES,
	ICON_AUDIO_HEADPHONES_SYMBOLIC,
	ICON_AUDIO_HEADSETS,
	ICON_AUDIO_HEADSET_SYMBOLIC,
	ICON_AUDIO_INPUT_MICROPHONE,
	ICON_AUDIO_INPUT_MICROPHONE_SYMBOLIC,
	ICON_AUDIO_SPEAKER_CENTER,
	ICON_AUDIO_SPEAKER_CENTER_BACK,
	ICON_AUDIO_SPEAKER_CENTER_BACK_TESTING,
	ICON_AUDIO_SPEAKER_CENTER_TESTING,
	ICON_AUDIO_SPEAKER_LEFT,
	ICON_AUDIO_SPEAKER_LEFT_BACK,
	ICON_AUDIO_SPEAKER_LEFT_BACK_TESTING,
	ICON_AUDIO_SPEAKER_LEFT_SIDE,
	ICON_AUDIO_SPEAKER_LEFT_SIDE_TESTING,
	ICON_AUDIO_SPEAKER_LEFT_TESTING,
	ICON_AUDIO_SPEAKER_RIGHT,
	ICON_AUDIO_SPEAKER_RIGHT_BACK,
	ICON_AUDIO_SPEAKER_RIGHT_BACK_TESTING,
	ICON_AUDIO_SPEAKER_RIGHT_SIDE,
	ICON_AUDIO_SPEAKER_RIGHT_SIDE_TESTING,
	ICON_AUDIO_SPEAKER_RIGHT_TESTING,
	ICON_AUDIO_SPEAKERS,
	ICON_AUDIO_SPEAKERS_SYMBOLIC,
	ICON_AUDIO_SUBWOOFER,
	ICON_AUDIO_SUBWOOFER_TESTING,
	ICON_BATTERY,
	ICON_BATTERY_SYMBOLIC,
	ICON_BLUETOOTH,
	ICON_BLUETOOTH_SYMBOLIC,
	ICON_CAMERA_PHOTO,
	ICON_CAMERA_PHOTO_SYMBOLIC,
	ICON_CAMERA_VIDEO,
	ICON_CAMERA_VIDEO_SYMBOLIC,
	ICON_CAMERA_WEB,
	ICON_CAMERA_WEB_SYMBOLIC,
	ICON_COLORIMETER_COLORHUG_SYMBOLIC,
	ICON_COMPUTER_LAPTOP,
	ICON_COMPUTER_LAPTOP_SYMBOLIC,
	ICON_DISPLAY_PROJECTOR_SYMBOLIC,
	ICON_DRIVE_HARDDISK,
	ICON_DRIVE_HARDDISK_IEEE1394_SYMBOLIC,
	ICON_DRIVE_HARDDISK_SOLIDSTATE,
	ICON_DRIVE_HARDDISK_SOLIDSTATE_SYMBOLIC,
	ICON_DRIVE_HARDDISK_SYMBOLIC,
	ICON_DRIVE_MULTIDISK_SYMBOLIC,
	ICON_DRIVE_OPTICAL_SYMBOLIC,
	ICON_DRIVE_REMOVABLE_MEDIA,
	ICON_DRIVE_REMOVABLE_MEDIA_SYMBOLIC,
	ICON_DRIVE_REMOVABLE_MEDIA_USB,
	ICON_FINGERPRINT,
	ICON_FINGERPRINT_SYMBOLIC,
	ICON_GNOME_DEV_PRINTER_NEW,
	ICON_INPUT_DIALPAD_SYMBOLIC,
	ICON_INPUT_GAMING,
	ICON_INPUT_GAMING_SYMBOLIC,
	ICON_INPUT_KEYBOARD,
	ICON_INPUT_KEYBOARD_SYMBOLIC,
	ICON_INPUT_MOUSE,
	ICON_INPUT_MOUSE_SYMBOLIC,
	ICON_INPUT_TABLET,
	ICON_INPUT_TABLET_SYMBOLIC,
	ICON_INPUT_TOUCHPAD,
	ICON_INPUT_TOUCHPAD_SYMBOLIC,
	ICON_MEDIA_FLASH_CF,
	ICON_MEDIA_FLASH_MS,
	ICON_MEDIA_FLASH_SYMBOLIC,
	ICON_MEDIA_FLOPPY_SYMBOLIC,
	ICON_MEDIA_MEMORY,
	ICON_MEDIA_MEMORY_SD,
	ICON_MEDIA_MEMORY_SEMBOLIC,
	ICON_MEDIA_MEMORY_SM,
	ICON_MEDIA_OPTICAL,
	ICON_MEDIA_OPTICAL_SYMBOLIC,
	ICON_MEDIA_REMOVABLE_SYMBOLIC,
	ICON_MEDIA_TAPE_SYMBOLIC,
	ICON_MEDIA_ZIP_SYMBOLIC,
	ICON_MODEM,
	ICON_MODEM_SYMBOLIC,
	ICON_MULTIMEDIA_PLAYER,
	ICON_MULTIMEDIA_PLAYER_SYMBOLIC,
	ICON_NETWORK_CELLULAR,
	ICON_NETWORK_FIREWALL,
	ICON_NETWORK_VPN,
	ICON_NETWORK_WIRED,
	ICON_NETWORK_WIRELESS,
	ICON_NETWORK_WIRELESS_HOTSPOT,
	ICON_NM_DEVICE_WWAN,
	ICON_PDA_SYMBOLIC,
	ICON_PHONE,
	ICON_PHONE_SYMBOLIC,
	ICON_PRINTER,
	ICON_PRINTER_NETWORK,
	ICON_PRINTER_SYMBOLIC,
	ICON_SCANNER,
	ICON_SCANNER_SYMBOLIC,
	ICON_TABLET,
	ICON_TABLET_SYMBOLIC,
	ICON_TV_SYMBOLIC,
	ICON_UNINTERRUPTIBLE_POWER_SUPPLY,
	ICON_UNINTERRUPTIBLE_POWER_SUPPLY_SYMBOLIC,
	ICON_VIDEO_DISPLAY,
	ICON_VIDEO_DISPLAY_SYMBOLIC,
	ICON_EMBLEM_DEFAULT_SYMBOLIC,
	ICON_EMBLEM_DOCUMENTS_SYMBOLIC,
	ICON_EMBLEM_FAVORITE_SYMBOLIC,
	ICON_EMBLEM_IMPORTANT_SYMBOLIC,
	ICON_EMBLEM_MUSIC_SYMBOLIC,
	ICON_EMBLEM_OK_SYMBOLIC,
	ICON_EMBLEM_PHOTOS_SYMBOLIC,
	ICON_EMBLEM_READONLY,
	ICON_EMBLEM_SHARED_SYMBOLIC,
	ICON_EMBLEM_SYMBOLIC_LINK,
	ICON_EMBLEM_SYNCHRONIZED,
	ICON_EMBLEM_SYNCHRONIZING_SYMBOLIC,
	ICON_EMBLEM_UNREADABLE,
	ICON_EMBLEM_VIDEOS_SYMBOLIC,
	ICON_FACE_ANGEL,
	ICON_FACE_ANGEL_SYMBOLIC,
	ICON_FACE_ANGRY,
	ICON_FACE_ANGRY_SYMBOLIC,
	ICON_FACE_COOL,
	ICON_FACE_COOL_SYMBOLIC,
	ICON_FACE_CRYING,
	ICON_FACE_CRYING_SYMBOLIC,
	ICON_FACE_DEVILISH,
	ICON_FACE_DEVILISH_SYMBOLIC,
	ICON_FACE_EMBARRASSED,
	ICON_FACE_EMBARRASSED_SYMBOLIC,
	ICON_FACE_HEART,
	ICON_FACE_HEART_BROKEN,
	ICON_FACE_HEART_BROKEN_SYMBOLIC,
	ICON_FACE_HEART_SYMBOLIC,
	ICON_FACE_KISS,
	ICON_FACE_KISS_SYMBOLIC,
	ICON_FACE_LAUGH,
	ICON_FACE_LAUGH_SYMBOLIC,
	ICON_FACE_MONKEY_SYMBOLIC,
	ICON_FACE_PLAIN,
	ICON_FACE_PLAIN_SYMBOLIC,
	ICON_FACE_RASPBERRY,
	ICON_FACE_RASPBERRY_SYMBOLIC,
	ICON_FACE_SAD,
	ICON_FACE_SAD_SYMBOLIC,
	ICON_FACE_SICK,
	ICON_FACE_SICK_SYMBOLIC,
	ICON_FACE_SMILE,
	ICON_FACE_SMILE_BIG,
	ICON_FACE_SMILE_BIG_SYMBOLIC,
	ICON_FACE_SMILE_SYMBOLIC,
	ICON_FACE_SMIRK,
	ICON_FACE_SMIRK_SYMBOLIC,
	ICON_FACE_SURPRISE,
	ICON_FACE_SURPRISE_SYMBOLIC,
	ICON_FACE_TIRED,
	ICON_FACE_TIRED_SYMBOLIC,
	ICON_FACE_UNCERTAIN,
	ICON_FACE_UNCERTAIN_SYMBOLIC,
	ICON_FACE_WINK,
	ICON_FACE_WINK_SYMBOLIC,
	ICON_FACE_WORRIED,
	ICON_FACE_WORRIED_SYMBOLIC,
	ICON_APPLICATION_CERTIFICATE_SYMBOLIC,
	ICON_APPLICATION_EPUB_ZIP,
	ICON_APPLICATION_ILLUSTRATOR,
	ICON_APPLICATION_JAVASCRIPT,
	ICON_APPLICATION_MSWORD,
	ICON_APPLICATION_OCTET_STREAM,
	ICON_APPLICATION_PDF,
	ICON_APPLICATION_PGP,
	ICON_APPLICATION_RSS_XML_SYMBOLIC,
	ICON_APPLICATION_VND,
	ICON_APPLICATION_X_APPLIANCE_SYMBOLIC,
	ICON_APPLICATION_X_BITTORRENT,
	ICON_APPLICATION_X_CD_IMAGE,
	ICON_APPLICATION_X_DESKTOP,
	ICON_APPLICATION_X_EXECUTABLE_SYMBOLIC,
	ICON_APPLICATION_X_FICTIONBOOK_XML,
	ICON_APPLICATION_X_FIRMWARE,
	ICON_APPLICATION_X_FIRMWARE_SYMBOLIC,
	ICON_APPLICATION_X_FLASH_VIDEO,
	ICON_APPLICATION_X_MS_DOS_EXECUTABLE,
	ICON_APPLICATION_X_PARTIAL_DOWNLOAD,
	ICON_APPLICATION_X_PHP,
	ICON_APPLICATION_X_RUBY,
	ICON_AUDIO_X_GENERIC,
	ICON_AUDIO_X_GENERIC_SYMBOLIC,
	ICON_AUDIO_X_PLAYLIST,
	ICON_EXTENSION,
	ICON_FONT_X_GENERIC,
	ICON_FONT_X_GENERIC_SYMBOLIC,
	ICON_IMAGE_VND,
	ICON_IMAGE_X_GENERIC,
	ICON_IMAGE_X_GENERIC_SYMBOLIC,
	ICON_IMAGE_X_XCF,
	ICON_INTERNET_FEED,
	ICON_MODEL,
	ICON_OFFICE_CONTACT,
	ICON_OFFICE_DATABASE,
	ICON_PACKAGE_X_GENERIC,
	ICON_PACKAGE_X_GENERIC_SYMBOLIC,
	ICON_PAYMENT_CARD,
	ICON_PAYMENT_CARD_AMEX,
	ICON_PAYMENT_CARD_DINERS_CLUB,
	ICON_PAYMENT_CARD_DISCOVER,
	ICON_PAYMENT_CARD_JCB,
	ICON_PAYMENT_CARD_MASTERCARD,
	ICON_PAYMENT_CARD_SYMBOLIC,
	ICON_PAYMENT_CARD_UNIONPAY,
	ICON_PAYMENT_CARD_VISA,
	ICON_TEXT,
	ICON_TEXT_CSS,
	ICON_TEXT_HTML,
	ICON_TEXT_HTML_SYMBOLIC,
	ICON_TEXT_MARKDOWN,
	ICON_TEXT_X_BIBTEX,
	ICON_TEXT_X_CHANGELOG,
	ICON_TEXT_X_CHDR,
	ICON_TEXT_X_COPYING,
	ICON_TEXT_X_COPYING_SYMBOLIC,
	ICON_TEXT_X_CSRC,
	ICON_TEXT_X_GENERIC_SYMBOLIC,
	ICON_TEXT_X_GENERIC_TEMPLATE,
	ICON_TEXT_X_GETTEXT_TRANSLATION,
	ICON_TEXT_X_GETTEXT_TRANSLATION_TEMPLATE,
	ICON_TEXT_X_GO,
	ICON_TEXT_X_INSTALL,
	ICON_TEXT_X_MAKEFILE,
	ICON_TEXT_X_PREVIEW,
	ICON_TEXT_X_PYTHON,
	ICON_TEXT_X_README,
	ICON_TEXT_X_SASS,
	ICON_TEXT_X_SCRIPT,
	ICON_TEXT_X_SSA,
	ICON_TEXT_X_TEX,
	ICON_TEXT_X_VALA,
	ICON_UNKNOWN,
	ICON_VIDEO_X_GENERIC,
	ICON_VIDEO_X_GENERIC_SYMBOLIC,
	ICON_X_OFFICE_ADDRESS_BOOK_SYMBOLIC,
	ICON_X_OFFICE_DOCUMENT,
	ICON_X_OFFICE_DOCUMENT_SYMBOLIC,
	ICON_X_OFFICE_DOCUMENT_TEMPLATE,
	ICON_X_OFFICE_DRAWING,
	ICON_X_OFFICE_DRAWING_SYMBOLIC,
	ICON_X_OFFICE_DRAWING_TEMPLATE,
	ICON_X_OFFICE_PRESENTATION,
	ICON_X_OFFICE_PRESENTATION_SYMBOLIC,
	ICON_X_OFFICE_PRESENTATION_TEMPLATE,
	ICON_X_OFFICE_SPREADSHEET,
	ICON_X_OFFICE_SPREADSHEET_SYMBOLIC,
	ICON_X_OFFICE_SPREADSHEET_TEMPLATE,
	ICON_BOOKMARK_MISSING,
	ICON_DISTRIBUTOR_LOGO,
	ICON_DISTRIBUTOR_LOGO_SYMBOLIC,
	ICON_FOLDER,
	ICON_FOLDER_DOCUMENTS,
	ICON_FOLDER_DOCUMENTS_OPEN,
	ICON_FOLDER_DOCUMENTS_SYMBOLIC,
	ICON_FOLDER_DOWNLOAD,
	ICON_FOLDER_DOWNLOAD_OPEN,
	ICON_FOLDER_DOWNLOAD_SYMBOLIC,
	ICON_FOLDER_MUSIC,
	ICON_FOLDER_MUSIC_OPEN,
	ICON_FOLDER_MUSIC_SYMBOLIC,
	ICON_FOLDER_OPEN,
	ICON_FOLDER_PICTURES,
	ICON_FOLDER_PICTURES_OPEN,
	ICON_FOLDER_PICTURES_SYMBOLIC,
	ICON_FOLDER_PUBLICSHARE,
	ICON_FOLDER_PUBLICSHARE_OPEN,
	ICON_FOLDER_PUBLICSHARE_SYMBOLIC,
	ICON_FOLDER_RECENT,
	ICON_FOLDER_RECENT_SYMBOLIC,
	ICON_FOLDER_REMOTE,
	ICON_FOLDER_REMOTE_OPEN,
	ICON_FOLDER_SAVED_SEARCH,
	ICON_FOLDER_SYMBOLIC,
	ICON_FOLDER_TAG,
	ICON_FOLDER_TEMPLATES,
	ICON_FOLDER_TEMPLATES_OPEN,
	ICON_FOLDER_TEMPLATES_SYMBOLIC,
	ICON_FOLDER_VIDEOS,
	ICON_FOLDER_VIDEOS_OPEN,
	ICON_FOLDER_VIDEOS_SYMBOLIC,
	ICON_INTERNET_RADIO,
	ICON_INTERNET_RADIO_SYMBOLIC,
	ICON_LIBRARY_AUDIOBOOK,
	ICON_LIBRARY_PLACES,
	ICON_LIBRARY_PODCAST,
	ICON_MAIL_INBOX,
	ICON_MAIL_INBOX_SYMBOLIC,
	ICON_MAIL_MAILBOX,
	ICON_MAIL_MAILBOX_SYMBOLIC,
	ICON_MAIL_OUTBOX,
	ICON_MAIL_OUTBOX_SYMBOLIC,
	ICON_NETWORK_SERVER_SYMBOLIC,
	ICON_PLAYLIST,
	ICON_PLAYLIST_AUTOMATIC,
	ICON_PLAYLIST_QUEUE,
	ICON_PLAYLIST_QUEUE_SYMBOLIC,
	ICON_PLAYLIST_SIMILAR,
	ICON_PLAYLIST_SYMBOLIC,
	ICON_TAG_SYMBOLIC,
	ICON_USER_BOOKMARKS_SYMBOLIC,
	ICON_USER_HOME,
	ICON_USER_HOME_OPEN,
	ICON_USER_HOME_SYMBOLIC,
	ICON_USER_TRASH,
	ICON_USER_TRASH_FULL,
	ICON_USER_TRASH_SYMBOLIC,
	ICON_AIRPLANE_MODE,
	ICON_AIRPLANE_MODE_SYMBOLIC,
	ICON_ALARM_SYMBOLIC,
	ICON_APPOINTMENT_MISSED,
	ICON_APPOINTMENT_MISSED_SYMBOLIC,
	ICON_APPOINTMENT_SOON,
	ICON_APPOINTMENT_SOON_SYMBOLIC,
	ICON_AUDIO_VOLUME_HIGH_SYMBOLIC,
	ICON_AUDIO_VOLUME_LOW_SYMBOLIC,
	ICON_AUDIO_VOLUME_MEDIUM_SYMBOLIC,
	ICON_AUDIO_VOLUME_MUTED_BLOCKING_SYMBOLIC,
	ICON_AUDIO_VOLUME_MUTED_SYMBOLIC,
	ICON_AVATAR_DEFAULT,
	ICON_AVATAR_DEFAULT_SYMBOLIC,
	ICON_BATTERY_AC_ADAPTER,
	ICON_BATTERY_AC_ADAPTER_SYMBOLIC,
	ICON_BATTERY_CAUTION,
	ICON_BATTERY_CAUTION_CHARGING,
	ICON_BATTERY_CAUTION_CHARGING_SYMBOLIC,
	ICON_BATTERY_CAUTION_SYMBOLIC,
	ICON_BATTERY_EMPTY,
	ICON_BATTERY_EMPTY_CHARGING,
	ICON_BATTERY_EMPTY_CHARGING_SYMBOLIC,
	ICON_BATTERY_EMPTY_SYMBOLIC,
	ICON_BATTERY_FULL,
	ICON_BATTERY_FULL_CHARGED,
	ICON_BATTERY_FULL_CHARGED_SYMBOLIC,
	ICON_BATTERY_FULL_CHARGING,
	ICON_BATTERY_FULL_CHARGING_SYMBOLIC,
	ICON_BATTERY_FULL_SYMBOLIC,
	ICON_BATTERY_GOOD,
	ICON_BATTERY_GOOD_CHARGING,
	ICON_BATTERY_GOOD_CHARGING_SYMBOLIC,
	ICON_BATTERY_GOOD_SYMBOLIC,
	ICON_BATTERY_LOW,
	ICON_BATTERY_LOW_CHARGING,
	ICON_BATTERY_LOW_CHARGING_SYMBOLIC,
	ICON_BATTERY_LOW_SYMBOLIC,
	ICON_BATTERY_MISSING,
	ICON_BATTERY_MISSING_SYMBOLIC,
	ICON_BLUETOOTH_ACTIVE_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED,
	ICON_BLUETOOTH_DISABLED_10_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_20_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_30_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_40_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_50_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_60_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_70_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_80_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_90_SYMBOLIC,
	ICON_BLUETOOTH_DISABLED_SYMBOLIC,
	ICON_BLUETOOTH_PAIRED_SYMBOLIC,
	ICON_CALL_MISSED_SYMBOLIC,
	ICON_CHANGES_ALLOW,
	ICON_CHANGES_ALLOW_SYMBOLIC,
	ICON_CHANGES_PREVENT_SYMBOLIC,
	ICON_CHANNEL_INSECURE_SYMBOLIC,
	ICON_CHANNEL_SECURE_SYMBOLIC,
	ICON_CHECK_ACTIVE_SYMBOLIC,
	ICON_CHECKBOX_CHECKED_SYMBOLIC,
	ICON_CHECKBOX_MIXED_SYMBOLIC,
	ICON_CHECKBOX_SYMBOLIC,
	ICON_CHECK_MIXED_SYMBOLIC,
	ICON_COMPUTER_FAIL_SYMBOLIC,
	ICON_CONTENT_LOADING_SYMBOLIC,
	ICON_DAYTIME_SUNRISE_SYMBOLIC,
	ICON_DAYTIME_SUNSET_SYMBOLIC,
	ICON_DIALOG_ERROR,
	ICON_DIALOG_ERROR_SYMBOLIC,
	ICON_DIALOG_INFORMATION,
	ICON_DIALOG_INFORMATION_SYMBOLIC,
	ICON_DIALOG_PASSWORD,
	ICON_DIALOG_PASSWORD_SYMBOLIC,
	ICON_DIALOG_WARNING,
	ICON_DIALOG_WARNING_SYMBOLIC,
	ICON_DISPLAY_BRIGHTNESS_SYMBOLIC,
	ICON_FOLDER_OPEN_SYMBOLIC,
	ICON_FOLDER_VISITING_SYMBOLIC,
	ICON_IMAGE_LOADING,
	ICON_IMAGE_MISSING,
	ICON_INPUT_KEYBOARD_CAPSLOCK_SYMBOLIC,
	ICON_INPUT_KEYBOARD_NUMLOCK_SYMBOLIC,
	ICON_KEYBOARD_BRIGHTNESS_SYMBOLIC,
	ICON_LOCATION_ACTIVE_SYMBOLIC,
	ICON_LOCATION_DISABLED_SYMBOLIC,
	ICON_LOCATION_INACTIVE_SYMBOLIC,
	ICON_LOCKED,
	ICON_MAIL_ATTACHMENT_SYMBOLIC,
	ICON_MAIL_FORWARDED_SYMBOLIC,
	ICON_MAIL_IMPORTANT_SYMBOLIC,
	ICON_MAIL_READ_SYMBOLIC,
	ICON_MAIL_REPLIED_SYMBOLIC,
	ICON_MAIL_UNREAD,
	ICON_MAIL_UNREAD_SYMBOLIC,
	ICON_MEDIA_PLAYLIST_CONSECUTIVE_SYMBOLIC,
	ICON_MEDIA_PLAYLIST_NO_REPEAT_SYMBOLIC,
	ICON_MEDIA_PLAYLIST_REPEAT,
	ICON_MEDIA_PLAYLIST_REPEAT_SONG_SYMBOLIC,
	ICON_MEDIA_PLAYLIST_REPEAT_SYMBOLIC,
	ICON_MEDIA_PLAYLIST_SHUFFLE_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_HIGH_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_LOW_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MEDIUM_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_10_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_20_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_30_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_40_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_50_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_60_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_70_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_80_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_90_SYMBOLIC,
	ICON_MICROPHONE_SENSITIVITY_MUTED_SYMBOLIC,
	ICON_NETWORK_CELLULAR_ACQUIRING_SYMBOLIC,
	ICON_NETWORK_CELLULAR_CONNECTED_SYMBOLIC,
	ICON_NETWORK_CELLULAR_NO_ROUTE_SYMBOLIC,
	ICON_NETWORK_CELLULAR_OFFLINE_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_EXCELLENT_SECURE_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_EXCELLENT_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_GOOD_SECURE_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_GOOD_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_NONE_SECURE_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_NONE_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_OK_SECURE_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_OK_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_WEAK_SECURE_SYMBOLIC,
	ICON_NETWORK_CELLULAR_SIGNAL_WEAK_SYMBOLIC,
	ICON_NETWORK_ERROR,
	ICON_NETWORK_ERROR_SYMBOLIC,
	ICON_NETWORK_IDLE,
	ICON_NETWORK_OFFLINE_SYMBOLIC,
	ICON_NETWORK_VPN_ACQUIRING_SYMBOLIC,
	ICON_NETWORK_VPN_LOCK_SYMBOLIC,
	ICON_NETWORK_VPN_SYMBOLIC,
	ICON_NETWORK_WIRED_ACQUIRING_SYMBOLIC,
	ICON_NETWORK_WIRED_DISCONNECTED,
	ICON_NETWORK_WIRED_NO_ROUTE_SYMBOLIC,
	ICON_NETWORK_WIRED_OFFLINE_SYMBOLIC,
	ICON_NETWORK_WIRED_SYMBOLIC,
	ICON_NETWORK_WIRELESS_ACQUIRING_SYMBOLIC,
	ICON_NETWORK_WIRELESS_CONNECTED_SYMBOLIC,
	ICON_NETWORK_WIRELESS_ENCRYPTED_SYMBOLIC,
	ICON_NETWORK_WIRELESS_HOTSPOT_SYMBOLIC,
	ICON_NETWORK_WIRELESS_NO_ROUTE_SYMBOLIC,
	ICON_NETWORK_WIRELESS_OFFLINE_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_EXCELLENT,
	ICON_NETWORK_WIRELESS_SIGNAL_EXCELLENT_SECURE_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_EXCELLENT_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_GOOD,
	ICON_NETWORK_WIRELESS_SIGNAL_GOOD_SECURE_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_GOOD_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_NONE_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_OK,
	ICON_NETWORK_WIRELESS_SIGNAL_OK_SECURE_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_OK_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_WEAK,
	ICON_NETWORK_WIRELESS_SIGNAL_WEAK_SECURE_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SIGNAL_WEAK_SYMBOLIC,
	ICON_NETWORK_WIRELESS_SYMBOLIC,
	ICON_NIGHT_LIGHT,
	ICON_NIGHT_LIGHT_DISABLED_10_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_20_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_30_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_40_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_50_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_60_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_70_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_80_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_90_SYMBOLIC,
	ICON_NIGHT_LIGHT_DISABLED_SYMBOLIC,
	ICON_NIGHT_LIGHT_SYMBOLIC,
	ICON_NM_NO_CONNECTION,
	ICON_NM_SIGNAL_0,
	ICON_NM_SIGNAL_0_SECURE,
	ICON_NM_SIGNAL_100,
	ICON_NM_SIGNAL_100_SECURE,
	ICON_NM_SIGNAL_25,
	ICON_NM_SIGNAL_25_SECURE,
	ICON_NM_SIGNAL_50,
	ICON_NM_SIGNAL_50_SECURE,
	ICON_NM_SIGNAL_75,
	ICON_NM_SIGNAL_75_SECURE,
	ICON_NM_VPN_ACTIVE_LOCK,
	ICON_NM_VPN_LOCK,
	ICON_NON_STARRED,
	ICON_NON_STARRED_SYMBOLIC,
	ICON_NOTIFICATION_AUDIO_VOLUME_HIGH,
	ICON_NOTIFICATION_AUDIO_VOLUME_LOW,
	ICON_NOTIFICATION_AUDIO_VOLUME_MEDIUM,
	ICON_NOTIFICATION_AUDIO_VOLUME_MUTED,
	ICON_NOTIFICATION_DEVICE_EJECT,
	ICON_NOTIFICATION_DISABLED,
	ICON_NOTIFICATION_DISABLED_10_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_20_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_30_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_40_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_50_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_60_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_70_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_80_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_90_SYMBOLIC,
	ICON_NOTIFICATION_DISABLED_SYMBOLIC,
	ICON_NOTIFICATION_DISPLAY_BRIGHTNESS,
	ICON_NOTIFICATION_KEYBOARD_BRIGHTNESS,
	ICON_NOTIFICATION_NETWORK_ETHERNET_DISCONNECTED,
	ICON_NOTIFICATION_NETWORK_WIRED,
	ICON_NOTIFICATION_NETWORK_WIRELESS,
	ICON_NOTIFICATION_NETWORK_WIRELESS_DISCONNECTED,
	ICON_NOTIFICATION_NETWORK_WIRELESS_DISCONNECTED_SYMBOLIC,
	ICON_NOTIFICATION_NETWORK_WIRELESS_SYMBOLIC,
	ICON_NOTIFICATION_NEW_10_SYMBOLIC,
	ICON_NOTIFICATION_NEW_20_SYMBOLIC,
	ICON_NOTIFICATION_NEW_30_SYMBOLIC,
	ICON_NOTIFICATION_NEW_40_SYMBOLIC,
	ICON_NOTIFICATION_NEW_50_SYMBOLIC,
	ICON_NOTIFICATION_NEW_60_SYMBOLIC,
	ICON_NOTIFICATION_NEW_70_SYMBOLIC,
	ICON_NOTIFICATION_NEW_80_SYMBOLIC,
	ICON_NOTIFICATION_NEW_90_SYMBOLIC,
	ICON_NOTIFICATION_NEW_SYMBOLIC,
	ICON_NOTIFICATION_SYMBOLIC,
	ICON_PAGER_CHECKED_SYMBOLIC,
	ICON_PRINTER_ERROR,
	ICON_PRINTER_ERROR_SYMBOLIC,
	ICON_PRINTER_PRINTING_SYMBOLIC,
	ICON_PRINTER_WARNING_SYMBOLIC,
	ICON_PROCESS_COMPLETED,
	ICON_PROCESS_COMPLETED_SYMBOLIC,
	ICON_PROCESS_ERROR_SYMBOLIC,
	ICON_PROCESS_WORKING_SYMBOLIC,
	ICON_RADIO_CHECKED_SYMBOLIC,
	ICON_RADIO_MIXED_SYMBOLIC,
	ICON_RADIO_SYMBOLIC,
	ICON_ROTATION_ALLOWED_SYMBOLIC,
	ICON_ROTATION_LOCKED_SYMBOLIC,
	ICON_SECURITY_HIGH,
	ICON_SECURITY_HIGH_SYMBOLIC,
	ICON_SECURITY_LOW,
	ICON_SECURITY_LOW_SYMBOLIC,
	ICON_SECURITY_MEDIUM,
	ICON_SECURITY_MEDIUM_SYMBOLIC,
	ICON_SEMI_STARRED,
	ICON_SEMI_STARRED_SYMBOLIC,
	ICON_SOFTWARE_UPDATE_AVAILABLE_SYMBOLIC,
	ICON_SOFTWARE_UPDATE_URGENT_SYMBOLIC,
	ICON_STARRED,
	ICON_STARRED_SYMBOLIC,
	ICON_TASK_DUE_SYMBOLIC,
	ICON_TASK_PAST_DUE_SYMBOLIC,
	ICON_TOUCHPAD_DISABLED_SYMBOLIC,
	ICON_USER_AVAILABLE,
	ICON_USER_AVAILABLE_SYMBOLIC,
	ICON_USER_AWAY,
	ICON_USER_AWAY_SYMBOLIC,
	ICON_USER_BUSY,
	ICON_USER_BUSY_SYMBOLIC,
	ICON_USER_IDLE_SYMBOLIC,
	ICON_USER_INVISIBLE,
	ICON_USER_INVISIBLE_SYMBOLIC,
	ICON_USER_OFFLINE,
	ICON_USER_OFFLINE_SYMBOLIC,
	ICON_USER_STATUS_PENDING_SYMBOLIC,
	ICON_USER_TRASH_FULL_SYMBOLIC,
	ICON_USER_TYPING,
	ICON_VIEW_PRIVATE,
	ICON_VIEW_PRIVATE_SYMBOLIC,
	ICON_VIEW_WRAPPED_SYMBOLIC,
	ICON_WEATHER_CLEAR_NIGHT_SYMBOLIC,
	ICON_WEATHER_CLEAR_SYMBOLIC,
	ICON_WEATHER_FEW_CLOUDS_NIGHT_SYMBOLIC,
	ICON_WEATHER_FEW_CLOUDS_SYMBOLIC,
	ICON_WEATHER_FOG_NIGHT_SYMBOLIC,
	ICON_WEATHER_FOG_SYMBOLIC,
	ICON_WEATHER_OVERCAST_NIGHT_SYMBOLIC,
	ICON_WEATHER_OVERCAST_SYMBOLIC,
	ICON_WEATHER_SEVERE_ALERT_SYMBOLIC,
	ICON_WEATHER_SHOWERS_NIGHT_SYMBOLIC,
	ICON_WEATHER_SHOWERS_SCATTERED_NIGHT_SYMBOLIC,
	ICON_WEATHER_SHOWERS_SCATTERED_SYMBOLIC,
	ICON_WEATHER_SHOWERS_SYMBOLIC,
	ICON_WEATHER_SNOW_NIGHT_SYMBOLIC,
	ICON_WEATHER_SNOW_SYMBOLIC,
	ICON_WEATHER_STORM_NIGHT_SYMBOLIC,
	ICON_WEATHER_STORM_SYMBOLIC,
	ICON_WEATHER_STORM_TORNADO_NIGHT_SYMBOLIC,
	ICON_WEATHER_STORM_TORNADO_SYMBOLIC,
	ICON_WEATHER_WINDY_SYMBOLIC,
}
using FatalError :: enum i32 {
	FATAL_ERROR_INVALID_BUFFER,
	FATAL_ERROR_UNKNOWN_SYSCALL,
	FATAL_ERROR_INVALID_MEMORY_REGION,
	FATAL_ERROR_MEMORY_REGION_LOCKED_BY_KERNEL,
	FATAL_ERROR_PATH_LENGTH_EXCEEDS_LIMIT,
	FATAL_ERROR_INVALID_HANDLE,
	FATAL_ERROR_MUTEX_NOT_ACQUIRED_BY_THREAD,
	FATAL_ERROR_MUTEX_ALREADY_ACQUIRED,
	FATAL_ERROR_SHARED_MEMORY_REGION_TOO_LARGE,
	FATAL_ERROR_INCORRECT_FILE_ACCESS,
	FATAL_ERROR_TOO_MANY_WAIT_OBJECTS,
	FATAL_ERROR_INCORRECT_NODE_TYPE,
	FATAL_ERROR_PROCESSOR_EXCEPTION,
	FATAL_ERROR_UNKNOWN,
	FATAL_ERROR_RECURSIVE_BATCH,
	FATAL_ERROR_CORRUPT_HEAP,
	FATAL_ERROR_CORRUPT_LINKED_LIST,
	FATAL_ERROR_SPINLOCK_NOT_ACQUIRED,
	FATAL_ERROR_UNKNOWN_SNAPSHOT_TYPE,
	FATAL_ERROR_PROCESS_ALREADY_ATTACHED,
	FATAL_ERROR_INSUFFICIENT_PERMISSIONS,
	FATAL_ERROR_ABORT,
	FATAL_ERROR_COUNT,
}
using SyscallType :: enum i32 {
	SYSCALL_ALLOCATE,
	SYSCALL_FREE,
	SYSCALL_SHARE_MEMORY,
	SYSCALL_MAP_OBJECT,
	SYSCALL_OPEN_SHARED_MEMORY,
	SYSCALL_MEMORY_COMMIT,
	SYSCALL_CREATE_PROCESS,
	SYSCALL_GET_CREATION_ARGUMENT,
	SYSCALL_TERMINATE_THREAD,
	SYSCALL_CREATE_THREAD,
	SYSCALL_WAIT,
	SYSCALL_TERMINATE_PROCESS,
	SYSCALL_CREATE_EVENT,
	SYSCALL_SET_EVENT,
	SYSCALL_RESET_EVENT,
	SYSCALL_POLL_EVENT,
	SYSCALL_PAUSE_PROCESS,
	SYSCALL_CRASH_PROCESS,
	SYSCALL_GET_THREAD_ID,
	SYSCALL_GET_PROCESS_STATE,
	SYSCALL_YIELD_SCHEDULER,
	SYSCALL_SLEEP,
	SYSCALL_OPEN_PROCESS,
	SYSCALL_SET_TLS,
	SYSCALL_GET_TLS,
	SYSCALL_TIMER_SET,
	SYSCALL_TIMER_CREATE,
	SYSCALL_GET_PROCESS_STATUS,
	SYSCALL_EVENT_FORWARD,
	SYSCALL_EVENT_SINK_CREATE,
	SYSCALL_EVENT_SINK_POP,
	SYSCALL_EVENT_SINK_PUSH,
	SYSCALL_AUDIO_STREAM_OPEN,
	SYSCALL_AUDIO_STREAM_NOTIFY,
	SYSCALL_GET_MESSAGE,
	SYSCALL_POST_MESSAGE,
	SYSCALL_POST_MESSAGE_REMOTE,
	SYSCALL_WAIT_MESSAGE,
	SYSCALL_GET_CURSOR_POSITION,
	SYSCALL_SET_CURSOR_POSITION,
	SYSCALL_GAME_CONTROLLER_STATE_POLL,
	SYSCALL_COPY,
	SYSCALL_GET_CLIPBOARD_HEADER,
	SYSCALL_PASTE_TEXT,
	SYSCALL_START_EYEDROP,
	SYSCALL_SET_SCREEN_WORK_AREA,
	SYSCALL_GET_SCREEN_WORK_AREA,
	SYSCALL_GET_SCREEN_BOUNDS,
	SYSCALL_FORCE_SCREEN_UPDATE,
	SYSCALL_SET_WINDOW_FRAME_BITMAP,
	SYSCALL_WINDOW_CREATE,
	SYSCALL_WINDOW_CLOSE,
	SYSCALL_WINDOW_REDRAW,
	SYSCALL_WINDOW_MOVE,
	SYSCALL_WINDOW_GET_ID,
	SYSCALL_WINDOW_GET_BOUNDS,
	SYSCALL_WINDOW_SET_SOLID,
	SYSCALL_WINDOW_SET_BITS,
	SYSCALL_WINDOW_SET_OPAQUE_BOUNDS,
	SYSCALL_WINDOW_SET_BLUR_BOUNDS,
	SYSCALL_WINDOW_SET_ALPHA,
	SYSCALL_WINDOW_SET_FOCUSED,
	SYSCALL_WINDOW_SET_METADATA,
	SYSCALL_WINDOW_SET_EMBED,
	SYSCALL_WINDOW_SET_CURSOR,
	SYSCALL_WINDOW_SET_OBJECT,
	SYSCALL_WINDOW_SET_EMBED_OWNER,
	SYSCALL_WINDOW_SET_RESIZE_CLEAR_COLOR,
	SYSCALL_OPEN_NODE,
	SYSCALL_OPEN_NODE_RELATIVE,
	SYSCALL_READ_FILE_SYNC,
	SYSCALL_WRITE_FILE_SYNC,
	SYSCALL_RESIZE_FILE,
	SYSCALL_REFRESH_NODE_INFORMATION,
	SYSCALL_ENUMERATE_DIRECTORY_CHILDREN,
	SYSCALL_DELETE_NODE,
	SYSCALL_MOVE_NODE,
	SYSCALL_DIRECTORY_MONITOR,
	SYSCALL_FILE_CONTROL,
	SYSCALL_DOMAIN_NAME_RESOLVE,
	SYSCALL_ECHO_REQUEST,
	SYSCALL_CONNECTION_OPEN,
	SYSCALL_CONNECTION_POLL,
	SYSCALL_CONNECTION_NOTIFY,
	SYSCALL_READ_CONSTANT_BUFFER,
	SYSCALL_SHARE_CONSTANT_BUFFER,
	SYSCALL_CREATE_CONSTANT_BUFFER,
	SYSCALL_MAILSLOT_SEND_DATA,
	SYSCALL_MAILSLOT_SEND_MESSAGE,
	SYSCALL_MAILSLOT_SHARE,
	SYSCALL_PIPE_CREATE,
	SYSCALL_PIPE_WRITE,
	SYSCALL_PIPE_READ,
	SYSCALL_USER_GET_HOME_FOLDER,
	SYSCALL_USER_LOGIN,
	SYSCALL_GET_SYSTEM_CONSTANTS,
	SYSCALL_TAKE_SYSTEM_SNAPSHOT,
	SYSCALL_SET_SYSTEM_CONSTANT,
	SYSCALL_GET_SYSTEM_INFORMATION,
	SYSCALL_SYSTEM_CONFIGURATION_WRITE,
	SYSCALL_SYSTEM_CONFIGURATION_READ,
	SYSCALL_PRINT,
	SYSCALL_CLOSE_HANDLE,
	SYSCALL_BATCH,
	SYSCALL_SHUTDOWN,
	SYSCALL_POSIX,
	SYSCALL_START_PROGRAM,
	SYSCALL_DEBUG_COMMAND,
	SYSCALL_COUNT,
}
using MessageType :: enum i32 {
	MSG_WM_START =  0x1000,
	MSG_MOUSE_MOVED =  0x1001,
	MSG_WINDOW_ACTIVATED =  0x1002,
	MSG_WINDOW_DEACTIVATED =  0x1003,
	MSG_WINDOW_DESTROYED =  0x1004,
	MSG_MOUSE_EXIT =  0x1006 ,
	MSG_WINDOW_RESIZED =  0x1007,
	MSG_MOUSE_LEFT_PRESSED =  0x1008	,
	MSG_MOUSE_LEFT_RELEASED =  0x1009,
	MSG_MOUSE_RIGHT_PRESSED =  0x100A,
	MSG_MOUSE_RIGHT_RELEASED =  0x100B,
	MSG_MOUSE_MIDDLE_PRESSED =  0x100C,
	MSG_MOUSE_MIDDLE_RELEASED =  0x100D ,
	MSG_KEY_PRESSED =  0x100E,
	MSG_KEY_RELEASED =  0x100F,
	MSG_UPDATE_WINDOW =  0x1010,
	MSG_WM_END =  0x13FF,
	MSG_PAINT =  0x2000	,
	MSG_PAINT_BACKGROUND =  0x2001	,
	MSG_PAINT_FOREGROUND =  0x2002	,
	MSG_GET_CURSOR =  0x2003	,
	MSG_ANIMATE =  0x2004	,
	MSG_Z_ORDER =  0x2005	,
	MSG_DESTROY =  0x2006	,
	MSG_MEASURE =  0x2008	,
	MSG_LAYOUT =  0x2009	,
	MSG_ENSURE_VISIBLE =  0x200A	,
	MSG_ADD_CHILD =  0x200B	,
	MSG_REMOVE_CHILD =  0x200C	,
	MSG_PRE_ADD_CHILD =  0x200D	,
	MSG_HIT_TEST =  0x200E	,
	MSG_MOUSE_DRAGGED =  0x200F	,
	MSG_CLICKED =  0x2010	,
	MSG_KEY_TYPED =  0x2011	,
	MSG_SCROLL_X =  0x2012	,
	MSG_SCROLL_Y =  0x2013	,
	MSG_STRONG_FOCUS_END =  0x2014	,
	MSG_BEFORE_Z_ORDER =  0x2015	,
	MSG_AFTER_Z_ORDER =  0x2016	,
	MSG_PAINT_CHILDREN =  0x2017	,
	MSG_DESTROY_CONTENTS =  0x2018	,
	MSG_STATE_CHANGE_MESSAGE_START =  0x2080,
	MSG_HOVERED_START =  0x2081	,
	MSG_HOVERED_END =  0x2082	,
	MSG_PRESSED_START =  0x2083	,
	MSG_PRESSED_END =  0x2084	,
	MSG_FOCUSED_START =  0x2085	,
	MSG_FOCUSED_END =  0x2086	,
	MSG_FOCUS_WITHIN_START =  0x2087	,
	MSG_FOCUS_WITHIN_END =  0x2088	,
	MSG_STATE_CHANGE_MESSAGE_END =  0x20FF,
	MSG_SCROLLBAR_MOVED =  0x3000	,
	MSG_CHECK_UPDATED =  0x3001	,
	MSG_RADIO_GROUP_UPDATED =  0x3002	,
	MSG_COLOR_CHANGED =  0x3003	,
	MSG_DESKTOP_START_PROGRAM =  0x4800,
	MSG_POWER_BUTTON_PRESSED =  0x4801,
	MSG_SET_WINDOW_METADATA =  0x4802,
	MSG_EMBEDDED_WINDOW_DESTROYED =  0x4803,
	MSG_FS_EVENT_START =  0x4B80,
	MSG_FS_CREATE =  0x4B81,
	MSG_FS_DELETE =  0x4B82,
	MSG_FS_MOVE =  0x4B83,
	MSG_FS_MODIFY =  0x4B84,
	MSG_FS_EVENT_END =  0x4B85,
	MSG_FS_MONITOR_DESTROY =  0x4B86 	,
	MSG_PROGRAM_CRASH =  0x4C00,
	MSG_CLIPBOARD_UPDATED =  0x5001,
	MSG_EYEDROP_REPORT =  0x5002,
	MSG_SYSTEM_CONSTANT_UPDATED =  0x5004,
	MSG_TIMER =  0x5006,
	MSG_PING =  0x5007	,
	MSG_RECEIVE_DATA =  0x5080,
	MSG_MAILSLOT_CLOSED =  0x5081,
	MSG_CHOICE_ADD_ITEMS =  0x5180,
	MSG_CHOICE_ITEM_TO_STRING =  0x5181,
	MSG_CHOICE_UPDATE =  0x5182,
	MSG_TEXTBOX_UPDATED =  0x5200,
	MSG_TEXTBOX_EDIT_START =  0x5201 	,
	MSG_TEXTBOX_EDIT_END =  0x5202 	,
	MSG_TEXTBOX_NUMBER_DRAG_START =  0x5203 	,
	MSG_TEXTBOX_NUMBER_DRAG_END =  0x5204 	,
	MSG_TEXTBOX_NUMBER_DRAG_DELTA =  0x5205 	,
	MSG_TEXTBOX_NUMBER_UPDATED =  0x5206 	,
	MSG_TEXTBOX_GET_BREADCRUMB =  0x5207 	,
	MSG_TEXTBOX_ACTIVATE_BREADCRUMB =  0x5208 	,
	MSG_LIST_VIEW_COMPARE_INDICES =  0x5300,
	MSG_LIST_VIEW_NEXT_INDEX =  0x5301,
	MSG_LIST_VIEW_PREVIOUS_INDEX =  0x5302,
	MSG_LIST_VIEW_FIRST_INDEX =  0x5303,
	MSG_LIST_VIEW_LAST_INDEX =  0x5304,
	MSG_LIST_VIEW_FIND_INDEX =  0x5305,
	MSG_LIST_VIEW_COUNT_ITEMS =  0x5306,
	MSG_LIST_VIEW_MEASURE_RANGE =  0x5307,
	MSG_LIST_VIEW_MEASURE_ITEM =  0x5308,
	MSG_LIST_VIEW_CREATE_ITEM =  0x5309,
	MSG_LIST_VIEW_GET_CONTENT =  0x530A,
	MSG_LIST_VIEW_GET_INDENT =  0x530B,
	MSG_LIST_VIEW_FIND_POSITION =  0x530C,
	MSG_LIST_VIEW_IS_SELECTED =  0x530D,
	MSG_LIST_VIEW_SELECT =  0x530E,
	MSG_LIST_VIEW_SELECT_RANGE =  0x530F,
	MSG_LIST_VIEW_CHOOSE_ITEM =  0x5310,
	MSG_LIST_VIEW_SEARCH =  0x5311,
	MSG_PROGRAM_EXIT =  0x7001,
	MSG_INSTANCE_CREATE =  0x7002,
	MSG_INSTANCE_OPEN =  0x7003,
	MSG_INSTANCE_SAVE =  0x7004,
	MSG_USER_START =  0x8000,
	MSG_USER_END =  0xBFFF,
}
using DrawMode :: enum i32 {
	DRAW_MODE_REPEAT_FIRST =  1 ,
	DRAW_MODE_STRECH,
	DRAW_MODE_REPEAT,
	DRAW_MODE_NONE,
}
using ClipboardFormat :: enum i32 {
	CLIPBOARD_FORMAT_EMPTY,
	CLIPBOARD_FORMAT_TEXT,
	CLIPBOARD_FORMAT_FILE_LIST,
}
using CursorStyle :: enum i32 {
	CURSOR_NORMAL,
	CURSOR_TEXT,
	CURSOR_RESIZE_VERTICAL,
	CURSOR_RESIZE_HORIZONTAL,
	CURSOR_RESIZE_DIAGONAL_1,
	CURSOR_RESIZE_DIAGONAL_2,
	CURSOR_SPLIT_VERTICAL,
	CURSOR_SPLIT_HORIZONTAL,
	CURSOR_HAND_HOVER,
	CURSOR_HAND_DRAG,
	CURSOR_HAND_POINT,
	CURSOR_SCROLL_UP_LEFT,
	CURSOR_SCROLL_UP,
	CURSOR_SCROLL_UP_RIGHT,
	CURSOR_SCROLL_LEFT,
	CURSOR_SCROLL_CENTER,
	CURSOR_SCROLL_RIGHT,
	CURSOR_SCROLL_DOWN_LEFT,
	CURSOR_SCROLL_DOWN,
	CURSOR_SCROLL_DOWN_RIGHT,
	CURSOR_SELECT_LINES,
	CURSOR_DROP_TEXT,
	CURSOR_CROSS_HAIR_PICK,
	CURSOR_CROSS_HAIR_RESIZE,
	CURSOR_MOVE_HOVER,
	CURSOR_MOVE_DRAG,
	CURSOR_ROTATE_HOVER,
	CURSOR_ROTATE_DRAG,
	CURSOR_BLANK,
	CURSOR_COUNT,
}
using WindowStyle :: enum i32 {
	WINDOW_NORMAL,
	WINDOW_CONTAINER,
	WINDOW_MENU,
	WINDOW_TIP,
	WINDOW_PLAIN,
}
using FormatValueType :: enum i32 {
	FORMAT_VALUE_INVALID,
	FORMAT_VALUE_STRING,
	FORMAT_VALUE_NUMBER,
	FORMAT_VALUE_TIME,
}
using CheckState :: enum i32 {
	CHECK_UNCHECKED =  0,
	CHECK_CHECKED =  1,
	CHECK_INDETERMINATE =  2,
}
using DocumentState :: enum i32 {
	DOCUMENT_STATE_EMPTY,
	DOCUMENT_STATE_UNSAVED,
	DOCUMENT_STATE_FILE,
}
using TransitionType :: enum i32 {
	TRANSITION_NONE,
	TRANSITION_SLIDE_UP,
	TRANSITION_SLIDE_DOWN,
	TRANSITION_COVER_UP,
	TRANSITION_COVER_DOWN,
	TRANSITION_SQUISH_UP,
	TRANSITION_SQUISH_DOWN,
	TRANSITION_ZOOM_OUT,
	TRANSITION_ZOOM_IN,
	TRANSITION_ZOOM_OUT_LIGHT,
	TRANSITION_ZOOM_IN_LIGHT,
	TRANSITION_REVEAL_UP,
	TRANSITION_REVEAL_DOWN,
	TRANSITION_FADE_IN,
	TRANSITION_FADE_OUT,
}
using MemoryProtection :: enum i32 {
	MEMORY_PROTECTION_READ_ONLY,
	MEMORY_PROTECTION_READ_WRITE,
	MEMORY_PROTECTION_EXECUTABLE,
}
UICallbackFunction :: distinct #type proc "c" (  ^Element,   ^Message,   ^Response);
BatchCall :: struct {
	index :   SyscallType,
	stopBatchIfError :   bool,
	using _ : struct #raw_union {
		argument0 :   uint     ,
		returnValue :   uint     ,
	},
	argument1 :   uint     ,
	argument2 :   uint     ,
	argument3 :   uint     ,
}
ThreadInformation :: struct {
	handle :   Handle,
	tid :   uint     ,
}
ProcessInformation :: struct {
	handle :   Handle,
	pid :   uint     ,
	mainThread :   ThreadInformation,
}
UniqueIdentifier :: struct {
	d : [16]u8     ,
}
NodeInformation :: struct {
	handle :   Handle,
	type :   NodeType,
	fileSize :   FileOffset,
	directoryChildren :   FileOffsetDifference,
}
DirectoryChild :: struct {
	name : [MAX_DIRECTORY_CHILD_NAME_LENGTH]i8  ,
	nameBytes :   int   ,
	information :   NodeInformation,
}
Point :: struct {
	x :   i32    ,
	y :   i32    ,
}
Rectangle :: struct {
	l :   i32    ,
	r :   i32    ,
	t :   i32    ,
	b :   i32    ,
}
Spinlock :: struct {
	state :   u8     ,
}
Mutex :: struct {
	event :   Handle,
	spinlock :   Spinlock,
	state :   u8     ,
	queued :   u32     ,
}
CrashReason :: struct {
	errorCode :   Error,
}
ProcessState :: struct {
	crashReason :   CrashReason,
	creationArgument :   Generic,
	id :   uint     ,
	executableState :   uint     ,
	flags :   u8     ,
}
ClipboardHeader :: struct {
	customBytes :   int   ,
	format :   ClipboardFormat,
	textBytes :   int   ,
	unused :   uint     ,
}
Painter :: struct {
	clip :   Rectangle,
	offsetX :   i32,
	offsetY :   i32,
	width :   i32,
	height :   i32,
	style :   rawptr,
	target :   ^PaintTarget,
}
Message :: struct {
	type :   MessageType,
	using _ : struct #raw_union {
		user : struct {
			context1 :   Generic,
			context2 :   Generic,
			context3 :   Generic,
		},
		_argument :   rawptr,
		mouseMoved : struct {
			oldPositionX :   i32,
			newPositionX :   i32,
			oldPositionY :   i32,
			newPositionY :   i32,
			newPositionXScreen :   i32,
			newPositionYScreen :   i32,
		},
		mouseDragged : struct {
			oldPositionX :   i32,
			newPositionX :   i32,
			oldPositionY :   i32,
			newPositionY :   i32,
			originalPositionX :   i32,
			originalPositionY :   i32,
		},
		mousePressed : struct {
			positionX :   i32,
			positionY :   i32,
			positionXScreen :   i32,
			positionYScreen :   i32,
			clickChainCount :   u8     ,
			alt :   u8     ,
			ctrl :   u8     ,
			shift :   u8     ,
			activationClick :   u8     ,
		},
		keyboard : struct {
			scancode :   u32     ,
			alt :   u8     ,
			ctrl :   u8     ,
			shift :   u8     ,
			numpad :   u8     ,
			repeat :   u8     ,
		},
		crash : struct {
			reason :   CrashReason,
			process :   Handle,
			pid :   uint     ,
		},
		clipboard :   ClipboardHeader,
		receive : struct {
			buffer :   Handle,
			bytes :   int   ,
		},
		animate : struct {
			deltaUs :   i64    ,
			waitUs :   i64    ,
			complete :   bool,
		},
		systemConstantUpdated : struct {
			index :   uint     ,
			newValue :   u64     ,
		},
		desktopExecute : struct {
			whatBuffer :   Handle,
			argumentBuffer :   Handle,
			mailslot :   Handle,
			whatBytes :   int   ,
			argumentBytes :   int   ,
			modalWindowParent :   u64     ,
		},
		windowMetadata : struct {
			id :   u64     ,
			buffer :   Handle,
			bytes :   int   ,
		},
		windowResized : struct {
			content :   Rectangle,
			hidden :   bool,
		},
		painter :   ^Painter,
		measure : struct {
			width :   i32,
			height :   i32,
			hideScrollbarX :   bool,
			hideScrollbarY :   bool,
		},
		child :   rawptr,
		hitTest : struct {
			x :   i32,
			y :   i32,
			inside :   bool,
		},
		zOrder : struct {
			index :   uint     ,
			child :   ^Element,
		},
		scrollbarMoved : struct {
			scroll :   i32,
			previous :   i32,
		},
		ensureVisible : struct {
			child :   ^Element,
		},
		cursorStyle :   CursorStyle,
		eyedrop : struct {
			color :   u32     ,
			cancelled :   bool,
		},
		colorChanged : struct {
			newColor :   u32     ,
			pickerClosed :   bool,
		},
		itemToString : struct {
			item :   Generic,
			text : string,
		},
		choiceUpdated : struct {
			newItem :   Generic,
			oldItem :   Generic,
		},
		numberDragDelta : struct {
			delta :   i32,
			hoverCharacter :   i32    ,
			fast :   bool,
		},
		numberUpdated : struct {
			delta :   f64   ,
			newValue :   f64   ,
		},
		getBreadcrumb : struct {
			index :   uint     ,
			text : string,
		},
		activateBreadcrumb :   uint     ,
		checkState :   CheckState,
		fsEvent : struct {
			buffer :   Handle,
			bufferBytes :   u16     ,
			pathOffset :   u16     ,
			oldPathOffset :   u16     ,
			pathBytes :   u16     ,
			oldPathBytes :   u16     ,
			_context :   Generic,
		},
		createInstance : struct {
			window :   Handle,
			data :   Handle,
			dataBytes :   int   ,
		},
		instanceOpen : struct {
			instance :   ^INSTANCE_TYPE,
			path : string,
		},
		instanceSave : struct {
			instance :   ^INSTANCE_TYPE,
			path : string,
		},
		compareIndices : struct {
			group :   i32    ,
			left :   Generic,
			right :   Generic,
			result :   i32,
		},
		iterateIndex : struct {
			group :   i32    ,
			index :   Generic,
			position :   i64    ,
		},
		itemRange : struct {
			group :   i32    ,
			firstIndex :   Generic,
			lastIndex :   Generic,
			result :   i64    ,
		},
		measureItem : struct {
			group :   i32    ,
			index :   Generic,
			result :   i64    ,
		},
		createItem : struct {
			group :   i32    ,
			index :   Generic,
			parent :   ^Element,
		},
		getContent : struct {
			index :   Generic,
			group :   i32    ,
			column :   u8     ,
			icon :   u32     ,
			text : string,
		},
		getIndent : struct {
			group :   i32    ,
			index :   Generic,
			indent :   u8     ,
		},
		selectRange : struct {
			fromIndex :   Generic,
			toIndex :   Generic,
			group :   i32    ,
			select :   bool,
			toggle :   bool,
		},
		selectItem : struct {
			group :   i32    ,
			index :   Generic,
			isSelected :   bool,
		},
		chooseItem : struct {
			group :   i32    ,
			index :   Generic,
		},
		searchItem : struct {
			group :   i32    ,
			index :   Generic,
			query : string,
		},
		layout : struct {
			sizeChanged :   bool,
		},
	},
}
_EsMessageWithObject :: struct {
	object :   rawptr,
	message :   Message,
}
DebuggerMessage :: struct {
	process :   Handle,
	reason :   CrashReason,
}
DriveInformation :: struct {
	name : [64]i8  ,
	nameBytes :   int   ,
	mountpoint : [256]i8  ,
	mountpointBytes :   int   ,
}
SnapshotProcessesItem :: struct {
	pid :   i64    ,
	memoryUsage :   i64    ,
	cpuTimeSlices :   i64    ,
	name : [SNAPSHOT_MAX_PROCESS_NAME_LENGTH]i8  ,
	nameLength :   int   ,
	internal :   u64     ,
}
SystemInformation :: struct {
	processCount :   uint     ,
	threadCount :   uint     ,
	handleCount :   uint     ,
	commitLimit :   uint     ,
	commit :   uint     ,
	countZeroedPages :   uint     ,
	countFreePages :   uint     ,
	countStandbyPages :   uint     ,
	countActivePages :   uint     ,
	coreHeapSize :   uint     ,
	coreHeapAllocations :   uint     ,
	fixedHeapSize :   uint     ,
	fixedHeapAllocations :   uint     ,
	coreRegions :   uint     ,
	kernelRegions :   uint     ,
}
SnapshotProcesses :: struct {
	count :   int   ,
	processes : []SnapshotProcessesItem,
}
_EsPOSIXSyscall :: struct {
	index :   int     ,
	arguments : [7]int     ,
}
ProcessCreationArguments :: struct {
	executablePath : string,
	environmentBlock :   rawptr,
	environmentBlockBytes :   int   ,
	creationArgument :   Generic,
	permissions :   u64     ,
}
_EsUserLoginArguments :: struct {
	name : string,
	home : string,
}
Instance :: struct {
	_private :   rawptr,
	window :   ^Window,
	documentState :   DocumentState,
}
PanelBand :: struct {
	preferredSize :   i32,
	minimumSize :   i32,
	maximumSize :   i32,
	grow :   i32,
	shrink :   i32,
}
ThemeMetrics :: struct {
	mask :   u64     ,
	insets :   Rectangle,
	clipInsets :   Rectangle,
	globalOffset :   Rectangle,
	clipEnabled :   i32,
	cursor :   i32,
	entranceTransition :   i32,
	exitTransition :   i32,
	entranceDuration :   i32,
	exitDuration :   i32,
	preferredWidth :   i32,
	preferredHeight :   i32,
	minimumWidth :   i32,
	minimumHeight :   i32,
	maximumWidth :   i32,
	maximumHeight :   i32,
	gapMajor :   i32,
	gapMinor :   i32,
	gapWrap :   i32,
	textColor :   i32,
	selectedBackground :   i32,
	selectedText :   i32,
	iconColor :   i32,
	textAlign :   i32,
	textSize :   i32,
	fontFamily :   i32,
	fontWeight :   i32,
	iconSize :   i32,
	isItalic :   bool,
	ellipsis :   bool,
	wrapText :   bool,
}
Font :: struct {
	family :   u16     ,
	weight :   u8     ,
	italic :   bool,
}
TextStyle :: struct {
	font :   Font,
	size :   u16     ,
	baselineOffset :   u16     ,
	kerning :   i8    ,
	figures :   u8     ,
	alternateDirection :   bool,
	color :   u32     ,
	blur :   u8     ,
	underlined :   bool,
}
TextRun :: struct {
	style :   TextStyle,
	bytes :   u32     ,
}
TextDisplayProperties :: struct {
	cLanguage :   cstring  ,
	rtl :   bool,
	trimTrailingSpaces :   bool,
	align :   u8     ,
	maxLines :   i32,
}
TextSelection :: struct {
	caret0 :   int      ,
	caret1 :   int      ,
	hideCaret :   bool,
	foreground :   u32     ,
	background :   u32     ,
}
FormatValue :: struct {
	type :   FormatValueType,
	isCString :   bool,
	isStringLiteral :   bool,
	using _ : struct #raw_union {
		string :   ^i8  ,
		number :   f64   ,
		time :   u64     ,
	},
}
FragmentShaderCallbackFunction :: distinct #type proc "c" (  i32,   i32,   ^StyledBox) ->   u32     ;
StyledBox :: struct {
	bounds :   Rectangle,
	clip :   Rectangle,
	backgroundColor :   u32     ,
	backgroundColor2 :   u32     ,
	fragmentShader :   FragmentShaderCallbackFunction,
	borderColor :   u32     ,
	borders :   Rectangle,
	cornerRadiusTopLeft :   i32,
	cornerRadiusTopRight :   i32,
	cornerRadiusBottomLeft :   i32,
	cornerRadiusBottomRight :   i32,
	blur :   bool,
	overwriteAlpha :   bool,
}
Arena :: struct {
	firstEmptySlot :   rawptr,
	firstBlock :   rawptr,
	slotsPerBlock :   int   ,
	slotSize :   int   ,
	blockSize :   int   ,
}
CalculationValue :: struct {
	error :   bool,
	number :   f64   ,
}
CommandCallbackFunction :: distinct #type proc "c" (  ^INSTANCE_TYPE,   ^Element,   ^Command);
Command :: struct {
	elements :   ^^Element,
	callback :   CommandCallbackFunction,
	disabled :   bool,
	registered :   bool,
	allocated :   bool,
	stableID :   u32     ,
	cKeyboardShortcut :   cstring  ,
	data :   Generic,
}
ListViewColumn :: struct {
	title : string,
	flags :   u64     ,
	width :   i32,
}
AudioFormat :: struct {
	sampleRate :   u32     ,
	sampleFormat :   u8     ,
	channels :   u8     ,
	reserved :   u16     ,
}
AudioStream :: struct {
	format :   AudioFormat,
	handle :   Handle,
	buffer :   ^u8     ,
	bufferBytes :   u32     ,
	readPointer :   u32     ,
	writePointer :   u32     ,
	status :   u16     ,
	control :   u16     ,
	clock :   u64     ,
}
ApplicationStartupInformation :: struct {
	id :   i64    ,
	filePath : string,
	targetWindow :   ^Window,
	flags :   u64     ,
}
INIState :: struct {
	buffer :   ^i8  ,
	sectionClass :   ^i8  ,
	section :   ^i8  ,
	key :   ^i8  ,
	value :   ^i8  ,
	bytes :   int   ,
	sectionClassBytes :   int   ,
	sectionBytes :   int   ,
	keyBytes :   int   ,
	valueBytes :   int   ,
}
SystemConfigurationItem :: struct {
	key :   ^i8  ,
	value :   ^i8  ,
	keyBytes :   int   ,
	valueBytes :   int   ,
}
SystemConfigurationGroup :: struct {
	section :   ^i8  ,
	sectionClass :   ^i8  ,
	sectionBytes :   int   ,
	sectionClassBytes :   int   ,
	items :   ^SystemConfigurationItem,
	itemCount :   int   ,
}
AnalogInput :: struct {
	x :   u8     ,
	y :   u8     ,
	z :   u8     ,
}
GameControllerState :: struct {
	id :   u64     ,
	buttonCount :   u8     ,
	analogCount :   u8     ,
	directionalPad :   u8     ,
	buttons :   u32     ,
	analog : [8]AnalogInput,
}
SchedulerLogEntry :: struct {
	path : [64]i8  ,
	thread : [16]i8  ,
	pathBytes :   u8     ,
	threadBytes :   u8     ,
	cpu :   u8     ,
	count :   u32     ,
	timeMs :   u64     ,
}
PCIDevice :: struct {
	deviceID :   u32     ,
	classCode :   u8     ,
	subclassCode :   u8     ,
	progIF :   u8     ,
	bus :   u8     ,
	slot :   u8     ,
	function :   u8     ,
	interruptPin :   u8     ,
	interruptLine :   u8     ,
	baseAddressesSizes : [6]int   ,
	baseAddresses : [6]u32     ,
	driverName : [64]i8  ,
	driverNameBytes :   int   ,
}
Address :: struct {
	using _ : struct #raw_union {
		using _  : struct {
			ipv4 :   u32     ,
			port :   u16     ,
		},
		d : [20]u8     ,
	},
}
Connection :: struct {
	address :   Address,
	receiveBufferBytes :   int   ,
	sendBufferBytes :   int   ,
	receiveBuffer :   ^u8     ,
	sendBuffer :   ^u8     ,
	receiveWritePointer :   uint     ,
	sendReadPointer :   uint     ,
	open :   bool,
	error :   Error,
	receiveReadPointer :   uint     ,
	sendWritePointer :   uint     ,
	handle :   Handle,
}
ThreadEntryFunction :: distinct #type proc "c" (  Generic);
ComparisonCallbackFunction :: distinct #type proc "c" (  rawptr,   rawptr,   Generic) ->   i32;
SwapCallbackFunction :: distinct #type proc "c" (  rawptr,   rawptr,   Generic);
CRTComparisonCallback :: distinct #type proc "c" (  rawptr,   rawptr) ->   i32;
TimerCallbackFunction :: distinct #type proc "c" (  Generic);
MenuCallbackFunction :: distinct #type proc "c" (  ^Menu,   Generic);
StorePullCallbackFunction :: distinct #type proc "c" (  ^Store,   ^INSTANCE_TYPE,   Generic,   i32,   Generic,   ^FormatValue);
StoreCallbackFunction :: distinct #type proc "c" (  ^Store,   ^INSTANCE_TYPE,   Generic,   Generic);
DirectoryMonitorCallbackFunction :: distinct #type proc "c" (  ^DirectoryMonitor,   i32,   ^i8  ,   int   ,   ^i8  ,   int   ,   Generic);
Batch :: #force_inline proc "c" (calls_ :   ^BatchCall, count_ :   int   ){ addr := 0x1000 + 0 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^BatchCall,   int   )) (fp))(calls_, count_); }
GetCreationArgument :: #force_inline proc "c" (object_ :   Handle) ->   Generic{ addr := 0x1000 + 1 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle) ->   Generic) (fp))(object_); }
GetSystemInformation :: #force_inline proc "c" (systemInformation_ :   ^SystemInformation){ addr := 0x1000 + 2 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^SystemInformation)) (fp))(systemInformation_); }
HandleClose :: #force_inline proc "c" (handle_ :   Handle) ->   Error{ addr := 0x1000 + 3 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle) ->   Error) (fp))(handle_); }
InitialiseCStandardLibrary :: #force_inline proc "c" (argc_ :   ^i32, argv_ :   ^^^i8  ){ addr := 0x1000 + 4 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^i32,   ^^^i8  )) (fp))(argc_, argv_); }
MailslotSendData :: #force_inline proc "c" (mailslot_ :   Handle, data_ :   rawptr, bytes_ :   int   ) ->   bool{ addr := 0x1000 + 5 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   rawptr,   int   ) ->   bool) (fp))(mailslot_, data_, bytes_); }
MakeLinuxSystemCall2 :: #force_inline proc "c" (n_ :   int     , a1_ :   int     , a2_ :   int     , a3_ :   int     , a4_ :   int     , a5_ :   int     , a6_ :   int     ) ->   int     { addr := 0x1000 + 6 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  int     ,   int     ,   int     ,   int     ,   int     ,   int     ,   int     ) ->   int     ) (fp))(n_, a1_, a2_, a3_, a4_, a5_, a6_); }
SystemGetConstant :: #force_inline proc "c" (index_ :   uint     ) ->   u64     { addr := 0x1000 + 8 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  uint     ) ->   u64     ) (fp))(index_); }
TakeSystemSnapshot :: #force_inline proc "c" (type_ :   i32, bufferSize_ :   ^int   ) ->   Handle{ addr := 0x1000 + 9 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32,   ^int   ) ->   Handle) (fp))(type_, bufferSize_); }
UserGetHomeFolder :: #force_inline proc "c" (buffer_ :   ^i8  , bufferBytes_ :   int   ) ->   int   { addr := 0x1000 + 10 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   int   ) ->   int   ) (fp))(buffer_, bufferBytes_); }
_EsInstanceCreate :: #force_inline proc "c" (bytes_ :   int   , message_ :   ^Message, cName_ :   cstring  ) ->   ^Instance{ addr := 0x1000 + 11 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  int   ,   ^Message,   cstring  ) ->   ^Instance) (fp))(bytes_, message_, cName_); }
_EsSyscall :: #force_inline proc "c" (a_ :   uint     , b_ :   uint     , c_ :   uint     , d_ :   uint     , e_ :   uint     , f_ :   uint     ) ->   uint     { addr := 0x1000 + 12 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  uint     ,   uint     ,   uint     ,   uint     ,   uint     ,   uint     ) ->   uint     ) (fp))(a_, b_, c_, d_, e_, f_); }
ApplicationStart :: #force_inline proc "c" (information_ :   ^ApplicationStartupInformation){ addr := 0x1000 + 124 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ApplicationStartupInformation)) (fp))(information_); }
SystemConfigurationReadAll :: #force_inline proc "c" (groupCount_ :   ^int   ) ->   ^SystemConfigurationGroup{ addr := 0x1000 + 164 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^int   ) ->   ^SystemConfigurationGroup) (fp))(groupCount_); }
SystemConfigurationReadInteger :: #force_inline proc "c" (section_ : string, key_ : string, defaultValue_ :   i64     = 0) ->   i64    { addr := 0x1000 + 295 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int, ^u8, int,   i64    ) ->   i64    ) (fp))(raw_data(section_), len(section_), raw_data(key_), len(key_), defaultValue_); }
SystemConfigurationGroupReadInteger :: #force_inline proc "c" (group_ :   ^SystemConfigurationGroup, key_ : string, defaultValue_ :   i64     = 0) ->   i64    { addr := 0x1000 + 296 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^SystemConfigurationGroup, ^u8, int,   i64    ) ->   i64    ) (fp))(group_, raw_data(key_), len(key_), defaultValue_); }
SystemConfigurationReadString :: #force_inline proc "c" (section_ : string, key_ : string, valueBytes_ :   ^int    = nil) ->   ^i8  { addr := 0x1000 + 297 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int, ^u8, int,   ^int   ) ->   ^i8  ) (fp))(raw_data(section_), len(section_), raw_data(key_), len(key_), valueBytes_); }
SystemConfigurationGroupReadString :: #force_inline proc "c" (group_ :   ^SystemConfigurationGroup, key_ : string, valueBytes_ :   ^int    = nil) ->   ^i8  { addr := 0x1000 + 298 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^SystemConfigurationGroup, ^u8, int,   ^int   ) ->   ^i8  ) (fp))(group_, raw_data(key_), len(key_), valueBytes_); }
INIParse :: #force_inline proc "c" (s_ :   ^INIState) ->   bool{ addr := 0x1000 + 7 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^INIState) ->   bool) (fp))(s_); }
INIPeek :: #force_inline proc "c" (s_ :   ^INIState) ->   bool{ addr := 0x1000 + 87 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^INIState) ->   bool) (fp))(s_); }
INIFormat :: #force_inline proc "c" (s_ :   ^INIState, buffer_ :   ^i8  , bytes_ :   int   ) ->   int   { addr := 0x1000 + 125 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^INIState,   ^i8  ,   int   ) ->   int   ) (fp))(s_, buffer_, bytes_); }
INIZeroTerminate :: #force_inline proc "c" (s_ :   ^INIState){ addr := 0x1000 + 126 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^INIState)) (fp))(s_); }
CommandAddButton :: #force_inline proc "c" (command_ :   ^Command, button_ :   ^Button){ addr := 0x1000 + 13 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Command,   ^Button)) (fp))(command_, button_); }
CommandByID :: #force_inline proc "c" (instance_ :   ^Instance, stableID_ :   u32     ) ->   ^Command{ addr := 0x1000 + 14 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Instance,   u32     ) ->   ^Command) (fp))(instance_, stableID_); }
CommandRegister :: #force_inline proc "c" (command_ :   ^Command, instance_ :   ^Instance, callback_ :   CommandCallbackFunction, stableID_ :   u32     , cDefaultKeyboardShortcut_ :   cstring   = nil, enabled_ :   bool = false) ->   ^Command{ addr := 0x1000 + 15 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Command,   ^Instance,   CommandCallbackFunction,   u32     ,   cstring  ,   bool) ->   ^Command) (fp))(command_, instance_, callback_, stableID_, cDefaultKeyboardShortcut_, enabled_); }
CommandSetCallback :: #force_inline proc "c" (command_ :   ^Command, callback_ :   CommandCallbackFunction){ addr := 0x1000 + 16 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Command,   CommandCallbackFunction)) (fp))(command_, callback_); }
CommandSetDisabled :: #force_inline proc "c" (command_ :   ^Command, disabled_ :   bool){ addr := 0x1000 + 17 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Command,   bool)) (fp))(command_, disabled_); }
DialogClose :: #force_inline proc "c" (window_ :   ^Window){ addr := 0x1000 + 18 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Window)) (fp))(window_); }
DialogShow :: #force_inline proc "c" (window_ :   ^Window) ->   ^Element{ addr := 0x1000 + 19 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Window) ->   ^Element) (fp))(window_); }
DialogShowAlert :: #force_inline proc "c" (window_ :   ^Window, title_ : string, content_ : string, iconID_ :   u32     , addOKButton_ :   bool = false) ->   ^Element{ addr := 0x1000 + 20 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Window, ^u8, int, ^u8, int,   u32     ,   bool) ->   ^Element) (fp))(window_, raw_data(title_), len(title_), raw_data(content_), len(content_), iconID_, addOKButton_); }
InstanceDestroy :: #force_inline proc "c" (instance_ :   ^INSTANCE_TYPE){ addr := 0x1000 + 300 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^INSTANCE_TYPE)) (fp))(instance_); }
KeyboardIsAltHeld :: #force_inline proc "c" () ->   bool{ addr := 0x1000 + 21 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   bool) (fp))(); }
KeyboardIsCtrlHeld :: #force_inline proc "c" () ->   bool{ addr := 0x1000 + 22 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   bool) (fp))(); }
KeyboardIsShiftHeld :: #force_inline proc "c" () ->   bool{ addr := 0x1000 + 23 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   bool) (fp))(); }
MessageGetInputText :: #force_inline proc "c" (message_ :   ^Message, buffer_ :   ^i8  ) ->   int   { addr := 0x1000 + 24 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Message,   ^i8  ) ->   int   ) (fp))(message_, buffer_); }
MessageMutexAcquire :: #force_inline proc "c" (){ addr := 0x1000 + 26 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" ()) (fp))(); }
MessageMutexCheck :: #force_inline proc "c" (){ addr := 0x1000 + 27 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" ()) (fp))(); }
MessageMutexRelease :: #force_inline proc "c" (){ addr := 0x1000 + 28 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" ()) (fp))(); }
MessagePost :: #force_inline proc "c" (target_ :   ^Element, message_ :   ^Message) ->   Error{ addr := 0x1000 + 29 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   ^Message) ->   Error) (fp))(target_, message_); }
MessagePostRemote :: #force_inline proc "c" (process_ :   Handle, message_ :   ^Message) ->   Error{ addr := 0x1000 + 30 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   ^Message) ->   Error) (fp))(process_, message_); }
MessageSend :: #force_inline proc "c" (object_ :   ^Element, message_ :   ^Message) ->   Response{ addr := 0x1000 + 31 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   ^Message) ->   Response) (fp))(object_, message_); }
MessageReceive :: #force_inline proc "c" () ->   ^Message{ addr := 0x1000 + 318 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   ^Message) (fp))(); }
MouseGetPosition :: #force_inline proc "c" (relativeElement_ :   ^Element = nil) ->   Point{ addr := 0x1000 + 32 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element) ->   Point) (fp))(relativeElement_); }
MouseSetPosition :: #force_inline proc "c" (relativeWindow_ :   ^Window, x_ :   i32, y_ :   i32){ addr := 0x1000 + 33 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Window,   i32,   i32)) (fp))(relativeWindow_, x_, y_); }
StyleInheritRegister :: #force_inline proc "c" (cStyle_ :   cstring  , cParent_ :   cstring  , customMetrics_ :   ^ThemeMetrics){ addr := 0x1000 + 239 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  cstring  ,   cstring  ,   ^ThemeMetrics)) (fp))(cStyle_, cParent_, customMetrics_); }
StyleRefreshAll :: #force_inline proc "c" (window_ :   ^Element){ addr := 0x1000 + 37 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element)) (fp))(window_); }
UISetDPI :: #force_inline proc "c" (dpiScale_ :   i32){ addr := 0x1000 + 38 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  i32)) (fp))(dpiScale_); }
DirectoryEnumerateChildren :: #force_inline proc "c" (path_ : string, buffer_ :   ^^DirectoryChild) ->   int      { addr := 0x1000 + 39 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   ^^DirectoryChild) ->   int      ) (fp))(raw_data(path_), len(path_), buffer_); }
DirectoryEnumerateChildrenFromHandle :: #force_inline proc "c" (directory_ :   Handle, buffer_ :   ^DirectoryChild, bufferCount_ :   int   ) ->   int      { addr := 0x1000 + 40 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   ^DirectoryChild,   int   ) ->   int      ) (fp))(directory_, buffer_, bufferCount_); }
DirectoryMonitorCreate :: #force_inline proc "c" (path_ : string, flags_ :   u32     , callback_ :   DirectoryMonitorCallbackFunction, _context_ :   Generic) ->   ^DirectoryMonitor{ addr := 0x1000 + 41 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   u32     ,   DirectoryMonitorCallbackFunction,   Generic) ->   ^DirectoryMonitor) (fp))(raw_data(path_), len(path_), flags_, callback_, _context_); }
DirectoryMonitorCreateFromHandle :: #force_inline proc "c" (directory_ :   Handle, flags_ :   u32     , callback_ :   DirectoryMonitorCallbackFunction, _context_ :   Generic) ->   ^DirectoryMonitor{ addr := 0x1000 + 42 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   u32     ,   DirectoryMonitorCallbackFunction,   Generic) ->   ^DirectoryMonitor) (fp))(directory_, flags_, callback_, _context_); }
DirectoryMonitorDestroy :: #force_inline proc "c" (monitor_ :   ^DirectoryMonitor){ addr := 0x1000 + 43 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^DirectoryMonitor)) (fp))(monitor_); }
FileControl :: #force_inline proc "c" (file_ :   Handle, flags_ :   u32     ) ->   Error{ addr := 0x1000 + 96 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   u32     ) ->   Error) (fp))(file_, flags_); }
FileReadAll :: #force_inline proc "c" (filePath_ : string, fileSize_ :   ^int   ) ->   rawptr{ addr := 0x1000 + 44 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   ^int   ) ->   rawptr) (fp))(raw_data(filePath_), len(filePath_), fileSize_); }
FileReadSync :: #force_inline proc "c" (file_ :   Handle, offset_ :   FileOffset, size_ :   int   , buffer_ :   rawptr) ->   int   { addr := 0x1000 + 45 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   FileOffset,   int   ,   rawptr) ->   int   ) (fp))(file_, offset_, size_, buffer_); }
FileResize :: #force_inline proc "c" (file_ :   Handle, newSize_ :   FileOffset) ->   Error{ addr := 0x1000 + 46 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   FileOffset) ->   Error) (fp))(file_, newSize_); }
FileWriteAll :: #force_inline proc "c" (filePath_ : string, data_ :   rawptr, fileSize_ :   int   ) ->   Error{ addr := 0x1000 + 47 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   rawptr,   int   ) ->   Error) (fp))(raw_data(filePath_), len(filePath_), data_, fileSize_); }
FileWriteAllGather :: #force_inline proc "c" (filePath_ : string, data_ :   ^rawptr, fileSize_ :   ^int   , gatherCount_ :   int   ) ->   Error{ addr := 0x1000 + 48 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   ^rawptr,   ^int   ,   int   ) ->   Error) (fp))(raw_data(filePath_), len(filePath_), data_, fileSize_, gatherCount_); }
FileWriteSync :: #force_inline proc "c" (file_ :   Handle, offset_ :   FileOffset, size_ :   int   , buffer_ :   rawptr) ->   int   { addr := 0x1000 + 50 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   FileOffset,   int   ,   rawptr) ->   int   ) (fp))(file_, offset_, size_, buffer_); }
NodeDelete :: #force_inline proc "c" (node_ :   Handle) ->   Error{ addr := 0x1000 + 51 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle) ->   Error) (fp))(node_); }
NodeDeleteByPath :: #force_inline proc "c" (filePath_ : string) ->   Error{ addr := 0x1000 + 52 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int) ->   Error) (fp))(raw_data(filePath_), len(filePath_)); }
NodeFindUniqueName :: #force_inline proc "c" (buffer_ :   ^i8  , originalBytes_ :   int   , bufferBytes_ :   int   ) ->   int   { addr := 0x1000 + 53 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   int   ,   int   ) ->   int   ) (fp))(buffer_, originalBytes_, bufferBytes_); }
NodeMove :: #force_inline proc "c" (node_ :   Handle, newDirectory_ :   Handle, newName_ : string) ->   Error{ addr := 0x1000 + 54 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   Handle, ^u8, int) ->   Error) (fp))(node_, newDirectory_, raw_data(newName_), len(newName_)); }
NodeOpen :: #force_inline proc "c" (path_ : string, flags_ :   u32     , information_ :   ^NodeInformation) ->   Error{ addr := 0x1000 + 55 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   u32     ,   ^NodeInformation) ->   Error) (fp))(raw_data(path_), len(path_), flags_, information_); }
NodeOpenRelative :: #force_inline proc "c" (directory_ :   Handle, path_ : string, flags_ :   u32     , information_ :   ^NodeInformation) ->   Error{ addr := 0x1000 + 56 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle, ^u8, int,   u32     ,   ^NodeInformation) ->   Error) (fp))(directory_, raw_data(path_), len(path_), flags_, information_); }
NodeRefreshInformation :: #force_inline proc "c" (information_ :   ^NodeInformation){ addr := 0x1000 + 57 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^NodeInformation)) (fp))(information_); }
ProcessCrash :: #force_inline proc "c" (error_ :   Error, message_ : string){ addr := 0x1000 + 58 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Error, ^u8, int)) (fp))(error_, raw_data(message_), len(message_)); }
ProcessCreate :: #force_inline proc "c" (executablePath_ : string, information_ :   ^ProcessInformation, argument_ :   Generic) ->   Error{ addr := 0x1000 + 59 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   ^ProcessInformation,   Generic) ->   Error) (fp))(raw_data(executablePath_), len(executablePath_), information_, argument_); }
ProcessCreate2 :: #force_inline proc "c" (arguments_ :   ^ProcessCreationArguments, information_ :   ^ProcessInformation) ->   Error{ addr := 0x1000 + 60 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^ProcessCreationArguments,   ^ProcessInformation) ->   Error) (fp))(arguments_, information_); }
ProcessGetExitStatus :: #force_inline proc "c" (process_ :   Handle) ->   i32{ addr := 0x1000 + 61 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle) ->   i32) (fp))(process_); }
ProcessGetID :: #force_inline proc "c" (process_ :   Handle) ->   uint     { addr := 0x1000 + 62 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle) ->   uint     ) (fp))(process_); }
ProcessGetState :: #force_inline proc "c" (process_ :   Handle, state_ :   ^ProcessState){ addr := 0x1000 + 63 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle,   ^ProcessState)) (fp))(process_, state_); }
ProcessOpen :: #force_inline proc "c" (pid_ :   u64     ) ->   Handle{ addr := 0x1000 + 64 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  u64     ) ->   Handle) (fp))(pid_); }
ProcessPause :: #force_inline proc "c" (process_ :   Handle, resume_ :   bool){ addr := 0x1000 + 65 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle,   bool)) (fp))(process_, resume_); }
ProcessTerminate :: #force_inline proc "c" (process_ :   Handle, status_ :   i32){ addr := 0x1000 + 66 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle,   i32)) (fp))(process_, status_); }
ProcessTerminateCurrent :: #force_inline proc "c" (){ addr := 0x1000 + 67 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" ()) (fp))(); }
ThreadCreate :: #force_inline proc "c" (entryFunction_ :   ThreadEntryFunction, information_ :   ^ThreadInformation, argument_ :   Generic) ->   Error{ addr := 0x1000 + 68 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ThreadEntryFunction,   ^ThreadInformation,   Generic) ->   Error) (fp))(entryFunction_, information_, argument_); }
ThreadGetID :: #force_inline proc "c" (thread_ :   Handle) ->   uint     { addr := 0x1000 + 69 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle) ->   uint     ) (fp))(thread_); }
ThreadTerminate :: #force_inline proc "c" (thread_ :   Handle){ addr := 0x1000 + 71 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle)) (fp))(thread_); }
ArenaAllocate :: #force_inline proc "c" (arena_ :   ^Arena, zero_ :   bool) ->   rawptr{ addr := 0x1000 + 72 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Arena,   bool) ->   rawptr) (fp))(arena_, zero_); }
ArenaFree :: #force_inline proc "c" (arena_ :   ^Arena, pointer_ :   rawptr){ addr := 0x1000 + 73 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Arena,   rawptr)) (fp))(arena_, pointer_); }
ArenaInitialise :: #force_inline proc "c" (arena_ :   ^Arena, blockSize_ :   int   , itemSize_ :   int   ){ addr := 0x1000 + 74 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Arena,   int   ,   int   )) (fp))(arena_, blockSize_, itemSize_); }
ConstantBufferCreate :: #force_inline proc "c" (data_ :   rawptr, dataBytes_ :   int   , targetProcess_ :   Handle) ->   Handle{ addr := 0x1000 + 75 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   int   ,   Handle) ->   Handle) (fp))(data_, dataBytes_, targetProcess_); }
ConstantBufferRead :: #force_inline proc "c" (constantBuffer_ :   Handle, output_ :   rawptr){ addr := 0x1000 + 76 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle,   rawptr)) (fp))(constantBuffer_, output_); }
ConstantBufferShare :: #force_inline proc "c" (constantBuffer_ :   Handle, targetProcess_ :   Handle) ->   Handle{ addr := 0x1000 + 77 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   Handle) ->   Handle) (fp))(constantBuffer_, targetProcess_); }
HeapAllocate :: #force_inline proc "c" (size_ :   int   , zeroMemory_ :   bool) ->   rawptr{ addr := 0x1000 + 78 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  int   ,   bool) ->   rawptr) (fp))(size_, zeroMemory_); }
HeapFree :: #force_inline proc "c" (address_ :   rawptr){ addr := 0x1000 + 79 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr)) (fp))(address_); }
HeapReallocate :: #force_inline proc "c" (oldAddress_ :   rawptr, newAllocationSize_ :   int   , zeroNewSpace_ :   bool) ->   rawptr{ addr := 0x1000 + 80 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   int   ,   bool) ->   rawptr) (fp))(oldAddress_, newAllocationSize_, zeroNewSpace_); }
HeapValidate :: #force_inline proc "c" (){ addr := 0x1000 + 81 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" ()) (fp))(); }
MemoryCommit :: #force_inline proc "c" (pointer_ :   rawptr, bytes_ :   int   ) ->   bool{ addr := 0x1000 + 82 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   int   ) ->   bool) (fp))(pointer_, bytes_); }
MemoryCompare :: #force_inline proc "c" (a_ :   rawptr, b_ :   rawptr, bytes_ :   int   ) ->   i32{ addr := 0x1000 + 83 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   rawptr,   int   ) ->   i32) (fp))(a_, b_, bytes_); }
MemoryCopy :: #force_inline proc "c" (destination_ :   rawptr, source_ :   rawptr, bytes_ :   int   ){ addr := 0x1000 + 84 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   rawptr,   int   )) (fp))(destination_, source_, bytes_); }
MemoryCopyReverse :: #force_inline proc "c" (_destination_ :   rawptr, _source_ :   rawptr, bytes_ :   int   ){ addr := 0x1000 + 85 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   rawptr,   int   )) (fp))(_destination_, _source_, bytes_); }
MemoryDecommit :: #force_inline proc "c" (pointer_ :   rawptr, bytes_ :   int   ) ->   bool{ addr := 0x1000 + 320 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   int   ) ->   bool) (fp))(pointer_, bytes_); }
MemoryFill :: #force_inline proc "c" (from_ :   rawptr, to_ :   rawptr, byte_ :   u8     ){ addr := 0x1000 + 86 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   rawptr,   u8     )) (fp))(from_, to_, byte_); }
MemoryMove :: #force_inline proc "c" (_start_ :   rawptr, _end_ :   rawptr, amount_ :   int     , zeroEmptySpace_ :   bool){ addr := 0x1000 + 88 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   rawptr,   int     ,   bool)) (fp))(_start_, _end_, amount_, zeroEmptySpace_); }
MemoryOpen :: #force_inline proc "c" (size_ :   int   , name_ : string, flags_ :   u32     ) ->   Handle{ addr := 0x1000 + 89 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  int   , ^u8, int,   u32     ) ->   Handle) (fp))(size_, raw_data(name_), len(name_), flags_); }
MemoryReserve :: #force_inline proc "c" (size_ :   int   , protection_ :   MemoryProtection = MEMORY_PROTECTION_READ_WRITE, commitAll_ :   bool = true) ->   rawptr{ addr := 0x1000 + 25 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  int   ,   MemoryProtection,   bool) ->   rawptr) (fp))(size_, protection_, commitAll_); }
MemoryShare :: #force_inline proc "c" (sharedMemoryRegion_ :   Handle, targetProcess_ :   Handle, readOnly_ :   bool) ->   Handle{ addr := 0x1000 + 90 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   Handle,   bool) ->   Handle) (fp))(sharedMemoryRegion_, targetProcess_, readOnly_); }
MemorySumBytes :: #force_inline proc "c" (data_ :   ^u8     , bytes_ :   int   ) ->   u8     { addr := 0x1000 + 91 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^u8     ,   int   ) ->   u8     ) (fp))(data_, bytes_); }
MemoryUnreserve :: #force_inline proc "c" (pointer_ :   rawptr, size_ :   int    = 0){ addr := 0x1000 + 319 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   int   )) (fp))(pointer_, size_); }
MemoryZero :: #force_inline proc "c" (destination_ :   rawptr, bytes_ :   int   ){ addr := 0x1000 + 92 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   int   )) (fp))(destination_, bytes_); }
ObjectMap :: #force_inline proc "c" (object_ :   Handle, offset_ :   uint     , size_ :   int   , flags_ :   u32     ) ->   rawptr{ addr := 0x1000 + 93 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   uint     ,   int   ,   u32     ) ->   rawptr) (fp))(object_, offset_, size_, flags_); }
AssertionFailure :: #force_inline proc "c" (cFile_ :   cstring  , line_ :   i32){ addr := 0x1000 + 94 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  cstring  ,   i32)) (fp))(cFile_, line_); }
DoubleParse :: #force_inline proc "c" (string_ : string, endptr_ :   ^^i8  ) ->   f64   { addr := 0x1000 + 97 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   ^^i8  ) ->   f64   ) (fp))(raw_data(string_), len(string_), endptr_); }
ExtractArguments :: #force_inline proc "c" (string_ :   ^i8  , bytes_ :   int   , delimiterByte_ :   u8     , replacementDelimiter_ :   u8     , argvAllocated_ :   int   , argv_ :   ^^i8  , argc_ :   ^int   ) ->   bool{ addr := 0x1000 + 98 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   int   ,   u8     ,   u8     ,   int   ,   ^^i8  ,   ^int   ) ->   bool) (fp))(string_, bytes_, delimiterByte_, replacementDelimiter_, argvAllocated_, argv_, argc_); }
RandomU8 :: #force_inline proc "c" () ->   u8     { addr := 0x1000 + 301 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   u8     ) (fp))(); }
RandomU64 :: #force_inline proc "c" () ->   u64     { addr := 0x1000 + 302 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   u64     ) (fp))(); }
IntegerParse :: #force_inline proc "c" (text_ : string) ->   i64    { addr := 0x1000 + 101 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int) ->   i64    ) (fp))(raw_data(text_), len(text_)); }
Print :: #force_inline proc "c" (format_ :   cstring  , _varargs_ : ..any){ addr := 0x1000 + 102 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  cstring  , ..any)) (fp))(format_, _varargs_); }
PrintDirect :: #force_inline proc "c" (string_ : string){ addr := 0x1000 + 103 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (^u8, int)) (fp))(raw_data(string_), len(string_)); }
PrintHelloWorld :: #force_inline proc "c" (){ addr := 0x1000 + 104 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" ()) (fp))(); }
RandomAddEntropy :: #force_inline proc "c" (x_ :   u64     ){ addr := 0x1000 + 106 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  u64     )) (fp))(x_); }
RandomSeed :: #force_inline proc "c" (x_ :   u64     ){ addr := 0x1000 + 107 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  u64     )) (fp))(x_); }
RectangleClip :: #force_inline proc "c" (parent_ :   Rectangle, rectangle_ :   Rectangle, output_ :   ^Rectangle) ->   bool{ addr := 0x1000 + 108 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Rectangle,   Rectangle,   ^Rectangle) ->   bool) (fp))(parent_, rectangle_, output_); }
Sort :: #force_inline proc "c" (_base_ :   rawptr, nmemb_ :   int   , size_ :   int   , compar_ :   ComparisonCallbackFunction, argument_ :   Generic){ addr := 0x1000 + 109 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   int   ,   int   ,   ComparisonCallbackFunction,   Generic)) (fp))(_base_, nmemb_, size_, compar_, argument_); }
SortWithSwapCallback :: #force_inline proc "c" (_base_ :   rawptr, nmemb_ :   int   , size_ :   int   , compar_ :   ComparisonCallbackFunction, argument_ :   Generic, swap_ :   SwapCallbackFunction){ addr := 0x1000 + 110 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   int   ,   int   ,   ComparisonCallbackFunction,   Generic,   SwapCallbackFunction)) (fp))(_base_, nmemb_, size_, compar_, argument_, swap_); }
TimeStamp :: #force_inline proc "c" () ->   u64     { addr := 0x1000 + 252 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   u64     ) (fp))(); }
TimeStampMs :: #force_inline proc "c" () ->   f64   { addr := 0x1000 + 261 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   f64   ) (fp))(); }
ColorBlend :: #force_inline proc "c" (under_ :   u32     , over_ :   u32     , fullAlpha_ :   bool) ->   u32     { addr := 0x1000 + 111 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  u32     ,   u32     ,   bool) ->   u32     ) (fp))(under_, over_, fullAlpha_); }
ColorConvertToRGB :: #force_inline proc "c" (h_ :   f32  , s_ :   f32  , v_ :   f32  ) ->   u32     { addr := 0x1000 + 112 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ,   f32  ,   f32  ) ->   u32     ) (fp))(h_, s_, v_); }
ColorConvertToHSV :: #force_inline proc "c" (color_ :   u32     , h_ :   ^f32  , s_ :   ^f32  , v_ :   ^f32  ) ->   bool{ addr := 0x1000 + 113 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  u32     ,   ^f32  ,   ^f32  ,   ^f32  ) ->   bool) (fp))(color_, h_, s_, v_); }
ColorParse :: #force_inline proc "c" (string_ : string) ->   u32     { addr := 0x1000 + 114 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int) ->   u32     ) (fp))(raw_data(string_), len(string_)); }
DrawBitmap :: #force_inline proc "c" (painter_ :   ^Painter, region_ :   Rectangle, bits_ :   ^u32     , stride_ :   uint     ){ addr := 0x1000 + 324 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Painter,   Rectangle,   ^u32     ,   uint     )) (fp))(painter_, region_, bits_, stride_); }
DrawInvert :: #force_inline proc "c" (painter_ :   ^Painter, bounds_ :   Rectangle){ addr := 0x1000 + 95 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Painter,   Rectangle)) (fp))(painter_, bounds_); }
DrawStandardIcon :: #force_inline proc "c" (painter_ :   ^Painter, id_ :   u32     , size_ :   i32, region_ :   Rectangle, color_ :   u32     ) ->   bool{ addr := 0x1000 + 115 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Painter,   u32     ,   i32,   Rectangle,   u32     ) ->   bool) (fp))(painter_, id_, size_, region_, color_); }
DrawStyledBox :: #force_inline proc "c" (painter_ :   ^Painter, box_ :   StyledBox){ addr := 0x1000 + 116 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Painter,   StyledBox)) (fp))(painter_, box_); }
DrawPaintTarget :: #force_inline proc "c" (painter_ :   ^Painter, source_ :   ^PaintTarget, destinationRegion_ :   Rectangle, sourceRegion_ :   Rectangle, borderRegion_ :   Rectangle, mode_ :   DrawMode, alpha_ :   u16     , clipRegion_ :   Rectangle){ addr := 0x1000 + 263 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Painter,   ^PaintTarget,   Rectangle,   Rectangle,   Rectangle,   DrawMode,   u16     ,   Rectangle)) (fp))(painter_, source_, destinationRegion_, sourceRegion_, borderRegion_, mode_, alpha_, clipRegion_); }
DrawText :: #force_inline proc "c" (painter_ :   ^Painter, plan_ :   ^TextPlan, bounds_ :   Rectangle, clip_ :   ^Rectangle = nil, selectionProperties_ :   ^TextSelection = nil){ addr := 0x1000 + 120 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Painter,   ^TextPlan,   Rectangle,   ^Rectangle,   ^TextSelection)) (fp))(painter_, plan_, bounds_, clip_, selectionProperties_); }
FontGetName :: #force_inline proc "c" (family_ :   u16     , buffer_ :   ^i8  , bufferBytes_ :   int   ) ->   int   { addr := 0x1000 + 121 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  u16     ,   ^i8  ,   int   ) ->   int   ) (fp))(family_, buffer_, bufferBytes_); }
IconIDFromString :: #force_inline proc "c" (string_ : string = "") ->   u32     { addr := 0x1000 + 127 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int) ->   u32     ) (fp))(raw_data(string_), len(string_)); }
ImageLoad :: #force_inline proc "c" (file_ :   ^u8     , fileSize_ :   int   , width_ :   ^u32     , height_ :   ^u32     , imageChannels_ :   i32) ->   ^u8     { addr := 0x1000 + 165 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^u8     ,   int   ,   ^u32     ,   ^u32     ,   i32) ->   ^u8     ) (fp))(file_, fileSize_, width_, height_, imageChannels_); }
PainterBoundsClient :: #force_inline proc "c" (painter_ :   ^Painter) ->   Rectangle{ addr := 0x1000 + 122 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Painter) ->   Rectangle) (fp))(painter_); }
PainterBoundsInset :: #force_inline proc "c" (painter_ :   ^Painter) ->   Rectangle{ addr := 0x1000 + 123 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Painter) ->   Rectangle) (fp))(painter_); }
PainterDrawStandardContent :: #force_inline proc "c" (painter_ :   ^Painter, text_ : string, iconID_ :   u32     ){ addr := 0x1000 + 117 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Painter, ^u8, int,   u32     )) (fp))(painter_, raw_data(text_), len(text_), iconID_); }
PaintTargetClear :: #force_inline proc "c" (target_ :   ^PaintTarget){ addr := 0x1000 + 34 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^PaintTarget)) (fp))(target_); }
PaintTargetEndDirectAccess :: #force_inline proc "c" (target_ :   ^PaintTarget){ addr := 0x1000 + 264 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^PaintTarget)) (fp))(target_); }
PaintTargetStartDirectAccess :: #force_inline proc "c" (target_ :   ^PaintTarget, bits_ :   ^^u32     , width_ :   ^int   , height_ :   ^int   , stride_ :   ^int   ){ addr := 0x1000 + 276 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^PaintTarget,   ^^u32     ,   ^int   ,   ^int   ,   ^int   )) (fp))(target_, bits_, width_, height_, stride_); }
PaintTargetTake :: #force_inline proc "c" (target_ :   ^PaintTarget, width_ :   int   , height_ :   int   ) ->   bool{ addr := 0x1000 + 35 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^PaintTarget,   int   ,   int   ) ->   bool) (fp))(target_, width_, height_); }
PaintTargetReturn :: #force_inline proc "c" (target_ :   ^PaintTarget){ addr := 0x1000 + 36 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^PaintTarget)) (fp))(target_); }
TextGetLineHeight :: #force_inline proc "c" (font_ :   Font, size_ :   u16     ) ->   i32{ addr := 0x1000 + 157 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Font,   u16     ) ->   i32) (fp))(font_, size_); }
TextGetPartialStringWidth :: #force_inline proc "c" (font_ :   Font, size_ :   u16     , fullString_ :   ^i8  , fullStringBytes_ :   int   , measureOffset_ :   uint     , measureBytes_ :   int   ) ->   i32{ addr := 0x1000 + 171 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Font,   u16     ,   ^i8  ,   int   ,   uint     ,   int   ) ->   i32) (fp))(font_, size_, fullString_, fullStringBytes_, measureOffset_, measureBytes_); }
TextGetCharacterAtPoint :: #force_inline proc "c" (font_ :   Font, size_ :   u16     , fullString_ :   ^i8  , fullStringBytes_ :   int   , measureOffset_ :   uint     , measureBytes_ :   int   , pointX_ :   ^i32, reverse_ :   bool, middle_ :   bool) ->   int      { addr := 0x1000 + 237 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Font,   u16     ,   ^i8  ,   int   ,   uint     ,   int   ,   ^i32,   bool,   bool) ->   int      ) (fp))(font_, size_, fullString_, fullStringBytes_, measureOffset_, measureBytes_, pointX_, reverse_, middle_); }
AudioStreamClose :: #force_inline proc "c" (stream_ :   ^AudioStream){ addr := 0x1000 + 128 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^AudioStream)) (fp))(stream_); }
AudioStreamOpen :: #force_inline proc "c" (device_ :   AudioDeviceID, bufferLengthUs_ :   int   ) ->   ^AudioStream{ addr := 0x1000 + 129 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  AudioDeviceID,   int   ) ->   ^AudioStream) (fp))(device_, bufferLengthUs_); }
AudioStreamNotify :: #force_inline proc "c" (stream_ :   ^AudioStream){ addr := 0x1000 + 130 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^AudioStream)) (fp))(stream_); }
AudioStreamSend :: #force_inline proc "c" (destination_ :   ^AudioStream, source_ :   ^AudioStream, time_ :   ^f64   ) ->   Error{ addr := 0x1000 + 131 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^AudioStream,   ^AudioStream,   ^f64   ) ->   Error) (fp))(destination_, source_, time_); }
AddressResolve :: #force_inline proc "c" (domain_ : string, flags_ :   u32     , address_ :   ^Address) ->   Error{ addr := 0x1000 + 99 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int,   u32     ,   ^Address) ->   Error) (fp))(raw_data(domain_), len(domain_), flags_, address_); }
ConnectionClose :: #force_inline proc "c" (connection_ :   ^Connection){ addr := 0x1000 + 321 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Connection)) (fp))(connection_); }
ConnectionNotify :: #force_inline proc "c" (connection_ :   ^Connection){ addr := 0x1000 + 322 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Connection)) (fp))(connection_); }
ConnectionOpen :: #force_inline proc "c" (connection_ :   ^Connection, flags_ :   u32     ) ->   Error{ addr := 0x1000 + 100 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Connection,   u32     ) ->   Error) (fp))(connection_, flags_); }
ConnectionPoll :: #force_inline proc "c" (connection_ :   ^Connection){ addr := 0x1000 + 303 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Connection)) (fp))(connection_); }
ConnectionRead :: #force_inline proc "c" (connection_ :   ^Connection, buffer_ :   rawptr, bufferBytes_ :   int   , bytesRead_ :   ^int   ) ->   Error{ addr := 0x1000 + 325 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Connection,   rawptr,   int   ,   ^int   ) ->   Error) (fp))(connection_, buffer_, bufferBytes_, bytesRead_); }
ConnectionWriteSync :: #force_inline proc "c" (connection_ :   ^Connection, data_ :   rawptr, dataBytes_ :   int   ) ->   Error{ addr := 0x1000 + 323 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Connection,   rawptr,   int   ) ->   Error) (fp))(connection_, data_, dataBytes_); }
GameControllerStatePoll :: #force_inline proc "c" (buffer_ :   ^GameControllerState) ->   int   { addr := 0x1000 + 299 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^GameControllerState) ->   int   ) (fp))(buffer_); }
EventCreate :: #force_inline proc "c" (autoReset_ :   bool) ->   Handle{ addr := 0x1000 + 132 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  bool) ->   Handle) (fp))(autoReset_); }
EventForward :: #force_inline proc "c" (event_ :   Handle, eventSink_ :   Handle, data_ :   Generic){ addr := 0x1000 + 133 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle,   Handle,   Generic)) (fp))(event_, eventSink_, data_); }
EventPoll :: #force_inline proc "c" (event_ :   Handle) ->   Error{ addr := 0x1000 + 134 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle) ->   Error) (fp))(event_); }
EventReset :: #force_inline proc "c" (event_ :   Handle){ addr := 0x1000 + 135 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle)) (fp))(event_); }
EventSet :: #force_inline proc "c" (event_ :   Handle){ addr := 0x1000 + 136 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle)) (fp))(event_); }
EventSinkCreate :: #force_inline proc "c" (ignoreDuplicates_ :   bool) ->   Handle{ addr := 0x1000 + 137 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  bool) ->   Handle) (fp))(ignoreDuplicates_); }
EventSinkPop :: #force_inline proc "c" (eventSink_ :   Handle, data_ :   ^Generic) ->   Error{ addr := 0x1000 + 138 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   ^Generic) ->   Error) (fp))(eventSink_, data_); }
EventSinkPush :: #force_inline proc "c" (eventSink_ :   Handle, data_ :   Generic) ->   Error{ addr := 0x1000 + 139 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  Handle,   Generic) ->   Error) (fp))(eventSink_, data_); }
MutexAcquire :: #force_inline proc "c" (mutex_ :   ^Mutex){ addr := 0x1000 + 140 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Mutex)) (fp))(mutex_); }
MutexDestroy :: #force_inline proc "c" (mutex_ :   ^Mutex){ addr := 0x1000 + 141 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Mutex)) (fp))(mutex_); }
MutexRelease :: #force_inline proc "c" (mutex_ :   ^Mutex){ addr := 0x1000 + 142 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Mutex)) (fp))(mutex_); }
PerformanceTimerPush :: #force_inline proc "c" (){ addr := 0x1000 + 143 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" ()) (fp))(); }
PerformanceTimerPop :: #force_inline proc "c" () ->   f64   { addr := 0x1000 + 144 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   f64   ) (fp))(); }
SchedulerYield :: #force_inline proc "c" (){ addr := 0x1000 + 145 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" ()) (fp))(); }
Sleep :: #force_inline proc "c" (milliseconds_ :   u64     ){ addr := 0x1000 + 146 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  u64     )) (fp))(milliseconds_); }
SpinlockAcquire :: #force_inline proc "c" (spinlock_ :   ^Spinlock){ addr := 0x1000 + 147 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Spinlock)) (fp))(spinlock_); }
SpinlockRelease :: #force_inline proc "c" (spinlock_ :   ^Spinlock){ addr := 0x1000 + 148 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Spinlock)) (fp))(spinlock_); }
TimerCreate :: #force_inline proc "c" () ->   Handle{ addr := 0x1000 + 149 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" () ->   Handle) (fp))(); }
TimerSet :: #force_inline proc "c" (handle_ :   Handle, afterMs_ :   u64     , callback_ :   TimerCallbackFunction, argument_ :   Generic){ addr := 0x1000 + 150 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  Handle,   u64     ,   TimerCallbackFunction,   Generic)) (fp))(handle_, afterMs_, callback_, argument_); }
Wait :: #force_inline proc "c" (objects_ :   ^Handle, objectCount_ :   int   , timeoutMs_ :   uint     ) ->   uint     { addr := 0x1000 + 151 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Handle,   int   ,   uint     ) ->   uint     ) (fp))(objects_, objectCount_, timeoutMs_); }
CStringLength :: #force_inline proc "c" (string_ :   cstring  ) ->   int   { addr := 0x1000 + 152 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  cstring  ) ->   int   ) (fp))(string_); }
StringAllocateAndFormat :: #force_inline proc "c" (bytes_ :   ^int   , format_ :   cstring  , _varargs_ : ..any) ->   ^i8  { addr := 0x1000 + 153 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^int   ,   cstring  , ..any) ->   ^i8  ) (fp))(bytes_, format_, _varargs_); }
StringCompare :: #force_inline proc "c" (s1_ : string, s2_ : string) ->   i32{ addr := 0x1000 + 155 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int, ^u8, int) ->   i32) (fp))(raw_data(s1_), len(s1_), raw_data(s2_), len(s2_)); }
StringCompareRaw :: #force_inline proc "c" (s1_ : string, s2_ : string) ->   i32{ addr := 0x1000 + 156 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int, ^u8, int) ->   i32) (fp))(raw_data(s1_), len(s1_), raw_data(s2_), len(s2_)); }
StringFormat :: #force_inline proc "c" (buffer_ :   ^i8  , bufferLength_ :   int   , format_ :   cstring  , _varargs_ : ..any) ->   int      { addr := 0x1000 + 158 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   int   ,   cstring  , ..any) ->   int      ) (fp))(buffer_, bufferLength_, format_, _varargs_); }
StringFormatTemporary :: #force_inline proc "c" (format_ :   cstring  , _varargs_ : ..any) ->   ^i8  { addr := 0x1000 + 159 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  cstring  , ..any) ->   ^i8  ) (fp))(format_, _varargs_); }
StringFormatAppend :: #force_inline proc "c" (buffer_ :   ^i8  , bufferLength_ :   int   , bufferPosition_ :   ^int   , format_ :   cstring  , _varargs_ : ..any) ->   bool{ addr := 0x1000 + 161 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   int   ,   ^int   ,   cstring  , ..any) ->   bool) (fp))(buffer_, bufferLength_, bufferPosition_, format_, _varargs_); }
StringLength :: #force_inline proc "c" (string_ :   ^i8  , end_ :   u8     ) ->   int   { addr := 0x1000 + 163 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   u8     ) ->   int   ) (fp))(string_, end_); }
StringStartsWith :: #force_inline proc "c" (string_ : string, prefix_ : string, caseInsensitive_ :   bool) ->   bool{ addr := 0x1000 + 166 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int, ^u8, int,   bool) ->   bool) (fp))(raw_data(string_), len(string_), raw_data(prefix_), len(prefix_), caseInsensitive_); }
StringZeroTerminate :: #force_inline proc "c" (string_ : string) ->   ^i8  { addr := 0x1000 + 167 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (^u8, int) ->   ^i8  ) (fp))(raw_data(string_), len(string_)); }
CRTabs :: #force_inline proc "c" (n_ :   i32) ->   i32{ addr := 0x1000 + 168 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32) ->   i32) (fp))(n_); }
CRTacosf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 169 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTasinf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 170 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTatan2f :: #force_inline proc "c" (y_ :   f32  , x_ :   f32  ) ->   f32  { addr := 0x1000 + 172 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ,   f32  ) ->   f32  ) (fp))(y_, x_); }
CRTatanf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 173 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTatoi :: #force_inline proc "c" (string_ :   ^i8  ) ->   i32{ addr := 0x1000 + 174 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ) ->   i32) (fp))(string_); }
CRTcalloc :: #force_inline proc "c" (num_ :   int   , size_ :   int   ) ->   rawptr{ addr := 0x1000 + 175 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  int   ,   int   ) ->   rawptr) (fp))(num_, size_); }
CRTceil :: #force_inline proc "c" (x_ :   f64   ) ->   f64   { addr := 0x1000 + 176 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f64   ) ->   f64   ) (fp))(x_); }
CRTceilf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 177 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTcosf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 178 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTexp :: #force_inline proc "c" (x_ :   f64   ) ->   f64   { addr := 0x1000 + 326 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f64   ) ->   f64   ) (fp))(x_); }
CRTexp2f :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 327 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTfabs :: #force_inline proc "c" (x_ :   f64   ) ->   f64   { addr := 0x1000 + 179 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f64   ) ->   f64   ) (fp))(x_); }
CRTfabsf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 344 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTfloor :: #force_inline proc "c" (x_ :   f64   ) ->   f64   { addr := 0x1000 + 181 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f64   ) ->   f64   ) (fp))(x_); }
CRTfloorf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 182 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTfmodf :: #force_inline proc "c" (x_ :   f32  , y_ :   f32  ) ->   f32  { addr := 0x1000 + 183 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ,   f32  ) ->   f32  ) (fp))(x_, y_); }
CRTfree :: #force_inline proc "c" (ptr_ :   rawptr){ addr := 0x1000 + 184 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr)) (fp))(ptr_); }
CRTgetenv :: #force_inline proc "c" (name_ :   ^i8  ) ->   ^i8  { addr := 0x1000 + 185 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ) ->   ^i8  ) (fp))(name_); }
CRTisalpha :: #force_inline proc "c" (c_ :   i32) ->   i32{ addr := 0x1000 + 186 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32) ->   i32) (fp))(c_); }
CRTisdigit :: #force_inline proc "c" (c_ :   i32) ->   i32{ addr := 0x1000 + 187 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32) ->   i32) (fp))(c_); }
CRTisnanf :: #force_inline proc "c" (f_ :   f32  ) ->   bool{ addr := 0x1000 + 345 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   bool) (fp))(f_); }
CRTisspace :: #force_inline proc "c" (c_ :   i32) ->   i32{ addr := 0x1000 + 188 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32) ->   i32) (fp))(c_); }
CRTisupper :: #force_inline proc "c" (c_ :   i32) ->   i32{ addr := 0x1000 + 189 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32) ->   i32) (fp))(c_); }
CRTisxdigit :: #force_inline proc "c" (c_ :   i32) ->   i32{ addr := 0x1000 + 190 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32) ->   i32) (fp))(c_); }
CRTmalloc :: #force_inline proc "c" (size_ :   int   ) ->   rawptr{ addr := 0x1000 + 191 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  int   ) ->   rawptr) (fp))(size_); }
CRTmemchr :: #force_inline proc "c" (_s_ :   rawptr, _c_ :   i32, n_ :   int   ) ->   rawptr{ addr := 0x1000 + 192 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   i32,   int   ) ->   rawptr) (fp))(_s_, _c_, n_); }
CRTmemcmp :: #force_inline proc "c" (s1_ :   rawptr, s2_ :   rawptr, n_ :   int   ) ->   i32{ addr := 0x1000 + 193 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   rawptr,   int   ) ->   i32) (fp))(s1_, s2_, n_); }
CRTmemcpy :: #force_inline proc "c" (dest_ :   rawptr, src_ :   rawptr, n_ :   int   ) ->   rawptr{ addr := 0x1000 + 194 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   rawptr,   int   ) ->   rawptr) (fp))(dest_, src_, n_); }
CRTmemmove :: #force_inline proc "c" (dest_ :   rawptr, src_ :   rawptr, n_ :   int   ) ->   rawptr{ addr := 0x1000 + 195 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   rawptr,   int   ) ->   rawptr) (fp))(dest_, src_, n_); }
CRTmemset :: #force_inline proc "c" (s_ :   rawptr, c_ :   i32, n_ :   int   ) ->   rawptr{ addr := 0x1000 + 196 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   i32,   int   ) ->   rawptr) (fp))(s_, c_, n_); }
CRTpowf :: #force_inline proc "c" (x_ :   f32  , y_ :   f32  ) ->   f32  { addr := 0x1000 + 328 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ,   f32  ) ->   f32  ) (fp))(x_, y_); }
CRTqsort :: #force_inline proc "c" (_base_ :   rawptr, nmemb_ :   int   , size_ :   int   , compar_ :   CRTComparisonCallback){ addr := 0x1000 + 197 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  rawptr,   int   ,   int   ,   CRTComparisonCallback)) (fp))(_base_, nmemb_, size_, compar_); }
CRTrealloc :: #force_inline proc "c" (ptr_ :   rawptr, size_ :   int   ) ->   rawptr{ addr := 0x1000 + 198 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  rawptr,   int   ) ->   rawptr) (fp))(ptr_, size_); }
CRTsinf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 199 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTsnprintf :: #force_inline proc "c" (buffer_ :   ^i8  , bufferSize_ :   int   , format_ :   ^i8  , _varargs_ : ..any) ->   i32{ addr := 0x1000 + 200 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   int   ,   ^i8  , ..any) ->   i32) (fp))(buffer_, bufferSize_, format_, _varargs_); }
CRTsprintf :: #force_inline proc "c" (buffer_ :   ^i8  , format_ :   ^i8  , _varargs_ : ..any) ->   i32{ addr := 0x1000 + 201 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^i8  , ..any) ->   i32) (fp))(buffer_, format_, _varargs_); }
CRTsqrt :: #force_inline proc "c" (x_ :   f64   ) ->   f64   { addr := 0x1000 + 202 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f64   ) ->   f64   ) (fp))(x_); }
CRTsqrtf :: #force_inline proc "c" (x_ :   f32  ) ->   f32  { addr := 0x1000 + 203 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  f32  ) ->   f32  ) (fp))(x_); }
CRTstrcat :: #force_inline proc "c" (dest_ :   ^i8  , src_ :   ^i8  ) ->   ^i8  { addr := 0x1000 + 205 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^i8  ) ->   ^i8  ) (fp))(dest_, src_); }
CRTstrchr :: #force_inline proc "c" (s_ :   ^i8  , c_ :   i32) ->   ^i8  { addr := 0x1000 + 206 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   i32) ->   ^i8  ) (fp))(s_, c_); }
CRTstrcmp :: #force_inline proc "c" (s1_ :   ^i8  , s2_ :   ^i8  ) ->   i32{ addr := 0x1000 + 207 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^i8  ) ->   i32) (fp))(s1_, s2_); }
CRTstrcpy :: #force_inline proc "c" (dest_ :   ^i8  , src_ :   ^i8  ) ->   ^i8  { addr := 0x1000 + 208 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^i8  ) ->   ^i8  ) (fp))(dest_, src_); }
CRTstrdup :: #force_inline proc "c" (string_ :   ^i8  ) ->   ^i8  { addr := 0x1000 + 70 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ) ->   ^i8  ) (fp))(string_); }
CRTstrerror :: #force_inline proc "c" (errnum_ :   i32) ->   ^i8  { addr := 0x1000 + 209 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32) ->   ^i8  ) (fp))(errnum_); }
CRTstrlen :: #force_inline proc "c" (s_ :   ^i8  ) ->   int   { addr := 0x1000 + 210 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ) ->   int   ) (fp))(s_); }
CRTstrncmp :: #force_inline proc "c" (s1_ :   ^i8  , s2_ :   ^i8  , n_ :   int   ) ->   i32{ addr := 0x1000 + 211 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^i8  ,   int   ) ->   i32) (fp))(s1_, s2_, n_); }
CRTstrncpy :: #force_inline proc "c" (dest_ :   ^i8  , src_ :   ^i8  , n_ :   int   ) ->   ^i8  { addr := 0x1000 + 212 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^i8  ,   int   ) ->   ^i8  ) (fp))(dest_, src_, n_); }
CRTstrnlen :: #force_inline proc "c" (s_ :   ^i8  , maxlen_ :   int   ) ->   int   { addr := 0x1000 + 213 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   int   ) ->   int   ) (fp))(s_, maxlen_); }
CRTstrstr :: #force_inline proc "c" (haystack_ :   ^i8  , needle_ :   ^i8  ) ->   ^i8  { addr := 0x1000 + 214 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^i8  ) ->   ^i8  ) (fp))(haystack_, needle_); }
CRTstrtol :: #force_inline proc "c" (nptr_ :   ^i8  , endptr_ :   ^^i8  , base_ :   i32) ->   i64 { addr := 0x1000 + 215 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^^i8  ,   i32) ->   i64 ) (fp))(nptr_, endptr_, base_); }
CRTstrtoul :: #force_inline proc "c" (nptr_ :   ^i8  , endptr_ :   ^^i8  , base_ :   i32) ->   u64     { addr := 0x1000 + 216 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^i8  ,   ^^i8  ,   i32) ->   u64     ) (fp))(nptr_, endptr_, base_); }
CRTtolower :: #force_inline proc "c" (c_ :   i32) ->   i32{ addr := 0x1000 + 217 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  i32) ->   i32) (fp))(c_); }
NewPanel :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil) ->   ^Panel{ addr := 0x1000 + 219 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ) ->   ^Panel) (fp))(parent_, flags_, cStyle_); }
NewSplitter :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil) ->   ^Splitter{ addr := 0x1000 + 220 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ) ->   ^Splitter) (fp))(parent_, flags_, cStyle_); }
NewButton :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil, label_ : string = "") ->   ^Button{ addr := 0x1000 + 221 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  , ^u8, int) ->   ^Button) (fp))(parent_, flags_, cStyle_, raw_data(label_), len(label_)); }
NewChoice :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil) ->   ^Choice{ addr := 0x1000 + 222 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ) ->   ^Choice) (fp))(parent_, flags_, cStyle_); }
NewColorWell :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil, colorRGB_ :   u32      = 0) ->   ^ColorWell{ addr := 0x1000 + 223 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ,   u32     ) ->   ^ColorWell) (fp))(parent_, flags_, cStyle_, colorRGB_); }
NewTextDisplay :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil, label_ : string = "") ->   ^TextDisplay{ addr := 0x1000 + 224 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  , ^u8, int) ->   ^TextDisplay) (fp))(parent_, flags_, cStyle_, raw_data(label_), len(label_)); }
NewIconDisplay :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil, iconID_ :   u32      = 0) ->   ^IconDisplay{ addr := 0x1000 + 225 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ,   u32     ) ->   ^IconDisplay) (fp))(parent_, flags_, cStyle_, iconID_); }
NewImageDisplay :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil) ->   ^ImageDisplay{ addr := 0x1000 + 226 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ) ->   ^ImageDisplay) (fp))(parent_, flags_, cStyle_); }
NewListView :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil, cItemStyle_ :   cstring   = nil, cHeaderItemStyle_ :   cstring   = nil, cFooterItemStyle_ :   cstring   = nil) ->   ^ListView{ addr := 0x1000 + 227 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ,   cstring  ,   cstring  ,   cstring  ) ->   ^ListView) (fp))(parent_, flags_, cStyle_, cItemStyle_, cHeaderItemStyle_, cFooterItemStyle_); }
NewTextbox :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil) ->   ^Textbox{ addr := 0x1000 + 228 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ) ->   ^Textbox) (fp))(parent_, flags_, cStyle_); }
NewCustomElement :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, cStyle_ :   cstring   = nil) ->   ^Element{ addr := 0x1000 + 229 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   cstring  ) ->   ^Element) (fp))(parent_, flags_, cStyle_); }
NewWindow :: #force_inline proc "c" (instance_ :   ^INSTANCE_TYPE, style_ :   WindowStyle) ->   ^Window{ addr := 0x1000 + 230 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^INSTANCE_TYPE,   WindowStyle) ->   ^Window) (fp))(instance_, style_); }
NewMenu :: #force_inline proc "c" (source_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT, userCallback_ :   MenuCallbackFunction = nil, _context_ :   Generic = nil, fixedWidth_ :   i32 = 0, fixedHeight_ :   i32 = 0) ->   ^Menu{ addr := 0x1000 + 231 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ,   MenuCallbackFunction,   Generic,   i32,   i32) ->   ^Menu) (fp))(source_, flags_, userCallback_, _context_, fixedWidth_, fixedHeight_); }
NewMenuItem :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64     , label_ : string = "", callback_ :   MenuCallbackFunction = nil, _context_ :   Generic = nil){ addr := 0x1000 + 232 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   u64     , ^u8, int,   MenuCallbackFunction,   Generic)) (fp))(parent_, flags_, raw_data(label_), len(label_), callback_, _context_); }
NewMenuCommand :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64     , label_ : string, command_ :   ^Command){ addr := 0x1000 + 233 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   u64     , ^u8, int,   ^Command)) (fp))(parent_, flags_, raw_data(label_), len(label_), command_); }
NewMenuColumn :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64      = FLAGS_DEFAULT) ->   ^Element{ addr := 0x1000 + 234 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ) ->   ^Element) (fp))(parent_, flags_); }
NewMenuSeparator :: #force_inline proc "c" (parent_ :   ^Element){ addr := 0x1000 + 235 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element)) (fp))(parent_); }
NewSpacer :: #force_inline proc "c" (parent_ :   ^Element, flags_ :   u64     ) ->   ^Element{ addr := 0x1000 + 236 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element,   u64     ) ->   ^Element) (fp))(parent_, flags_); }
NewTextPlan :: #force_inline proc "c" (properties_ :   ^TextDisplayProperties, bounds_ :   Rectangle, string_ :   ^i8  , textRuns_ :   ^TextRun, textRunCount_ :   int   , singleUse_ :   bool) ->   ^TextPlan{ addr := 0x1000 + 238 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^TextDisplayProperties,   Rectangle,   ^i8  ,   ^TextRun,   int   ,   bool) ->   ^TextPlan) (fp))(properties_, bounds_, string_, textRuns_, textRunCount_, singleUse_); }
ElementDraw :: #force_inline proc "c" (element_ :   ^Element, painter_ :   ^Painter){ addr := 0x1000 + 240 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   ^Painter)) (fp))(element_, painter_); }
ElementFocus :: #force_inline proc "c" (element_ :   ^Element, ensureVisible_ :   bool = false){ addr := 0x1000 + 243 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   bool)) (fp))(element_, ensureVisible_); }
ElementSetDisabled :: #force_inline proc "c" (element_ :   ^Element, disabled_ :   bool = true){ addr := 0x1000 + 244 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   bool)) (fp))(element_, disabled_); }
ElementSetHidden :: #force_inline proc "c" (element_ :   ^Element, hidden_ :   bool = true){ addr := 0x1000 + 245 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   bool)) (fp))(element_, hidden_); }
ElementSetCallback :: #force_inline proc "c" (element_ :   ^Element, callback_ :   UICallbackFunction){ addr := 0x1000 + 246 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   UICallbackFunction)) (fp))(element_, callback_); }
ElementGetSize :: #force_inline proc "c" (element_ :   ^Element, width_ :   ^i32, height_ :   ^i32){ addr := 0x1000 + 247 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   ^i32,   ^i32)) (fp))(element_, width_, height_); }
ElementRepaint :: #force_inline proc "c" (element_ :   ^Element, all_ :   bool, region_ :   Rectangle){ addr := 0x1000 + 248 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   bool,   Rectangle)) (fp))(element_, all_, region_); }
ElementSetCellRange :: #force_inline proc "c" (element_ :   ^Element, xFrom_ :   i32, yFrom_ :   i32, xTo_ :   i32 = -1, yTo_ :   i32 = -1){ addr := 0x1000 + 251 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   i32,   i32,   i32,   i32)) (fp))(element_, xFrom_, yFrom_, xTo_, yTo_); }
ElementGetInsets :: #force_inline proc "c" (element_ :   ^Element) ->   Rectangle{ addr := 0x1000 + 253 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element) ->   Rectangle) (fp))(element_); }
ElementGetInsetSize :: #force_inline proc "c" (element_ :   ^Element) ->   Rectangle{ addr := 0x1000 + 254 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element) ->   Rectangle) (fp))(element_); }
ElementGetMetrics :: #force_inline proc "c" (element_ :   ^Element) ->   ThemeMetrics{ addr := 0x1000 + 105 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element) ->   ThemeMetrics) (fp))(element_); }
ElementGetPreferredSize :: #force_inline proc "c" (element_ :   ^Element) ->   Rectangle{ addr := 0x1000 + 255 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element) ->   Rectangle) (fp))(element_); }
ElementMove :: #force_inline proc "c" (element_ :   ^Element, x_ :   i32, y_ :   i32, width_ :   i32, height_ :   i32){ addr := 0x1000 + 256 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element,   i32,   i32,   i32,   i32)) (fp))(element_, x_, y_, width_, height_); }
ElementGetLayoutParent :: #force_inline proc "c" (element_ :   ^Element) ->   ^Element{ addr := 0x1000 + 257 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element) ->   ^Element) (fp))(element_); }
ElementDestroy :: #force_inline proc "c" (element_ :   ^Element){ addr := 0x1000 + 258 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element)) (fp))(element_); }
ElementDestroyContents :: #force_inline proc "c" (element_ :   ^Element){ addr := 0x1000 + 259 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Element)) (fp))(element_); }
ElementStartAnimating :: #force_inline proc "c" (element_ :   ^Element) ->   bool{ addr := 0x1000 + 260 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Element) ->   bool) (fp))(element_); }
WindowGetBounds :: #force_inline proc "c" (window_ :   ^Window) ->   Rectangle{ addr := 0x1000 + 265 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Window) ->   Rectangle) (fp))(window_); }
WindowGetToolbar :: #force_inline proc "c" (window_ :   ^Window) ->   ^Element{ addr := 0x1000 + 266 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Window) ->   ^Element) (fp))(window_); }
WindowSetIcon :: #force_inline proc "c" (window_ :   ^Window, iconID_ :   u32     ){ addr := 0x1000 + 267 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Window,   u32     )) (fp))(window_, iconID_); }
WindowSetTitle :: #force_inline proc "c" (window_ :   ^Window, title_ : string = ""){ addr := 0x1000 + 268 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Window, ^u8, int)) (fp))(window_, raw_data(title_), len(title_)); }
MenuGetSource :: #force_inline proc "c" (menu_ :   ^Menu) ->   ^Element{ addr := 0x1000 + 269 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Menu) ->   ^Element) (fp))(menu_); }
ButtonSetIcon :: #force_inline proc "c" (button_ :   ^Button, iconID_ :   u32     ){ addr := 0x1000 + 270 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Button,   u32     )) (fp))(button_, iconID_); }
ButtonSetCheck :: #force_inline proc "c" (button_ :   ^Button, checkState_ :   CheckState = CHECK_CHECKED, sendUpdatedMessage_ :   bool = true){ addr := 0x1000 + 271 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Button,   CheckState,   bool)) (fp))(button_, checkState_, sendUpdatedMessage_); }
ButtonGetCheck :: #force_inline proc "c" (button_ :   ^Button) ->   CheckState{ addr := 0x1000 + 272 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Button) ->   CheckState) (fp))(button_); }
ButtonOnCommand :: #force_inline proc "c" (button_ :   ^Button, callback_ :   CommandCallbackFunction, command_ :   ^Command = nil){ addr := 0x1000 + 273 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Button,   CommandCallbackFunction,   ^Command)) (fp))(button_, callback_, command_); }
ButtonSetCheckBuddy :: #force_inline proc "c" (button_ :   ^Button, checkBuddy_ :   ^Element){ addr := 0x1000 + 274 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Button,   ^Element)) (fp))(button_, checkBuddy_); }
ButtonGetCheckBuddy :: #force_inline proc "c" (button_ :   ^Button) ->   ^Element{ addr := 0x1000 + 275 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Button) ->   ^Element) (fp))(button_); }
TextboxFind :: #force_inline proc "c" (textbox_ :   ^Textbox, string_ : string, line_ :   ^i32    , byte_ :   ^i32    , flags_ :   u32     ) ->   bool{ addr := 0x1000 + 277 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Textbox, ^u8, int,   ^i32    ,   ^i32    ,   u32     ) ->   bool) (fp))(textbox_, raw_data(string_), len(string_), line_, byte_, flags_); }
TextboxInsert :: #force_inline proc "c" (textbox_ :   ^Textbox, string_ : string = "", sendUpdatedMessage_ :   bool = true){ addr := 0x1000 + 278 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox, ^u8, int,   bool)) (fp))(textbox_, raw_data(string_), len(string_), sendUpdatedMessage_); }
TextboxGetContents :: #force_inline proc "c" (textbox_ :   ^Textbox, bytes_ :   ^int    = nil) ->   ^i8  { addr := 0x1000 + 279 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Textbox,   ^int   ) ->   ^i8  ) (fp))(textbox_, bytes_); }
TextboxGetLineLength :: #force_inline proc "c" (textbox_ :   ^Textbox, line_ :   uint      = 0) ->   int   { addr := 0x1000 + 280 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Textbox,   uint     ) ->   int   ) (fp))(textbox_, line_); }
TextboxGetSelection :: #force_inline proc "c" (textbox_ :   ^Textbox, fromLine_ :   ^i32    , fromByte_ :   ^i32    , toLine_ :   ^i32    , toByte_ :   ^i32    ){ addr := 0x1000 + 281 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox,   ^i32    ,   ^i32    ,   ^i32    ,   ^i32    )) (fp))(textbox_, fromLine_, fromByte_, toLine_, toByte_); }
TextboxMoveCaret :: #force_inline proc "c" (textbox_ :   ^Textbox, line_ :   i32    , byte_ :   i32    ){ addr := 0x1000 + 282 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox,   i32    ,   i32    )) (fp))(textbox_, line_, byte_); }
TextboxSetSelection :: #force_inline proc "c" (textbox_ :   ^Textbox, fromLine_ :   i32    , fromByte_ :   i32    , toLine_ :   i32    , toByte_ :   i32    ){ addr := 0x1000 + 283 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox,   i32    ,   i32    ,   i32    ,   i32    )) (fp))(textbox_, fromLine_, fromByte_, toLine_, toByte_); }
TextboxSelectAll :: #force_inline proc "c" (textbox_ :   ^Textbox){ addr := 0x1000 + 284 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox)) (fp))(textbox_); }
TextboxClear :: #force_inline proc "c" (textbox_ :   ^Textbox, sendUpdatedMessage_ :   bool){ addr := 0x1000 + 285 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox,   bool)) (fp))(textbox_, sendUpdatedMessage_); }
TextboxUseNumberOverlay :: #force_inline proc "c" (textbox_ :   ^Textbox, defaultBehaviour_ :   bool){ addr := 0x1000 + 286 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox,   bool)) (fp))(textbox_, defaultBehaviour_); }
TextboxUseBreadcrumbOverlay :: #force_inline proc "c" (textbox_ :   ^Textbox){ addr := 0x1000 + 287 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox)) (fp))(textbox_); }
TextboxMoveCaretRelative :: #force_inline proc "c" (textbox_ :   ^Textbox, flags_ :   u32     ){ addr := 0x1000 + 118 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox,   u32     )) (fp))(textbox_, flags_); }
TextboxEnsureCaretVisible :: #force_inline proc "c" (textbox_ :   ^Textbox){ addr := 0x1000 + 119 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Textbox)) (fp))(textbox_); }
PanelSetBands :: #force_inline proc "c" (panel_ :   ^Panel, columnCount_ :   int   , rowCount_ :   int    = 0, columns_ :   ^PanelBand = nil, rows_ :   ^PanelBand = nil){ addr := 0x1000 + 288 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Panel,   int   ,   int   ,   ^PanelBand,   ^PanelBand)) (fp))(panel_, columnCount_, rowCount_, columns_, rows_); }
PanelSwitchTo :: #force_inline proc "c" (panel_ :   ^Panel, targetChild_ :   ^Element, transitionType_ :   TransitionType, destroyPreviousAfterTransitionCompletes_ :   bool = false){ addr := 0x1000 + 289 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Panel,   ^Element,   TransitionType,   bool)) (fp))(panel_, targetChild_, transitionType_, destroyPreviousAfterTransitionCompletes_); }
TextPlanGetWidth :: #force_inline proc "c" (plan_ :   ^TextPlan) ->   i32{ addr := 0x1000 + 290 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^TextPlan) ->   i32) (fp))(plan_); }
TextPlanGetHeight :: #force_inline proc "c" (plan_ :   ^TextPlan) ->   i32{ addr := 0x1000 + 291 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^TextPlan) ->   i32) (fp))(plan_); }
TextPlanGetLineCount :: #force_inline proc "c" (plan_ :   ^TextPlan) ->   int   { addr := 0x1000 + 292 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^TextPlan) ->   int   ) (fp))(plan_); }
TextPlanDestroy :: #force_inline proc "c" (plan_ :   ^TextPlan){ addr := 0x1000 + 293 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^TextPlan)) (fp))(plan_); }
TextDisplaySetContents :: #force_inline proc "c" (display_ :   ^TextDisplay, contents_ : string = ""){ addr := 0x1000 + 294 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^TextDisplay, ^u8, int)) (fp))(display_, raw_data(contents_), len(contents_)); }
ColorWellGetRGB :: #force_inline proc "c" (well_ :   ^ColorWell) ->   u32     { addr := 0x1000 + 304 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^ColorWell) ->   u32     ) (fp))(well_); }
ColorWellSetRGB :: #force_inline proc "c" (well_ :   ^ColorWell, colorRGB_ :   u32     , sendChangedMessage_ :   bool){ addr := 0x1000 + 305 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ColorWell,   u32     ,   bool)) (fp))(well_, colorRGB_, sendChangedMessage_); }
ColorWellSetIndeterminate :: #force_inline proc "c" (well_ :   ^ColorWell){ addr := 0x1000 + 306 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ColorWell)) (fp))(well_); }
ChoiceAddMenuItem :: #force_inline proc "c" (choice_ :   ^Choice, item_ :   Generic){ addr := 0x1000 + 307 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Choice,   Generic)) (fp))(choice_, item_); }
ChoiceSetItem :: #force_inline proc "c" (choice_ :   ^Choice, item_ :   Generic, sendUpdatedMessage_ :   bool = true){ addr := 0x1000 + 308 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^Choice,   Generic,   bool)) (fp))(choice_, item_, sendUpdatedMessage_); }
ChoiceGetItem :: #force_inline proc "c" (choice_ :   ^Choice) ->   Generic{ addr := 0x1000 + 309 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^Choice) ->   Generic) (fp))(choice_); }
ListViewInsertGroup :: #force_inline proc "c" (view_ :   ^ListView, group_ :   i32    , flags_ :   u32      = FLAGS_DEFAULT){ addr := 0x1000 + 310 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ListView,   i32    ,   u32     )) (fp))(view_, group_, flags_); }
ListViewInsert :: #force_inline proc "c" (view_ :   ^ListView, group_ :   i32    , firstIndex_ :   Generic, lastIndex_ :   Generic, count_ :   i64     = -1){ addr := 0x1000 + 311 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ListView,   i32    ,   Generic,   Generic,   i64    )) (fp))(view_, group_, firstIndex_, lastIndex_, count_); }
ListViewRemove :: #force_inline proc "c" (view_ :   ^ListView, group_ :   i32    , firstIndex_ :   Generic, lastIndex_ :   Generic, count_ :   i64     = -1){ addr := 0x1000 + 312 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ListView,   i32    ,   Generic,   Generic,   i64    )) (fp))(view_, group_, firstIndex_, lastIndex_, count_); }
ListViewRemoveAll :: #force_inline proc "c" (view_ :   ^ListView, group_ :   i32    ){ addr := 0x1000 + 313 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ListView,   i32    )) (fp))(view_, group_); }
ListViewGetIndex :: #force_inline proc "c" (view_ :   ^ListView, item_ :   ^Element) ->   Generic{ addr := 0x1000 + 314 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); return ((proc "c" (  ^ListView,   ^Element) ->   Generic) (fp))(view_, item_); }
ListViewSetColumns :: #force_inline proc "c" (view_ :   ^ListView, columns_ :   ^ListViewColumn, columnCount_ :   int   ){ addr := 0x1000 + 315 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ListView,   ^ListViewColumn,   int   )) (fp))(view_, columns_, columnCount_); }
ListViewSetEmptyMessage :: #force_inline proc "c" (view_ :   ^ListView, message_ : string = ""){ addr := 0x1000 + 262 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ListView, ^u8, int)) (fp))(view_, raw_data(message_), len(message_)); }
ListViewSelect :: #force_inline proc "c" (view_ :   ^ListView, group_ :   i32    , index_ :   Generic){ addr := 0x1000 + 316 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ListView,   i32    ,   Generic)) (fp))(view_, group_, index_); }
ListViewResetSearchBuffer :: #force_inline proc "c" (view_ :   ^ListView){ addr := 0x1000 + 317 * size_of(int); fp := (rawptr(((^uintptr)(uintptr(addr)))^)); ((proc "c" (  ^ListView)) (fp))(view_); }
