package hash

@(optimization_mode="favor_size")
crc64_ecma_182 :: proc "contextless" (data: []byte, seed := u64(0)) -> (result: u64) #no_bounds_check {
	result = seed
	#no_bounds_check for b in data {
		result = result<<8 ~ _crc64_table_ecma_182[((result>>56) ~ u64(b)) & 0xff]
	}
	return result
}

/*
	Compute CRC-64 in the manner of xz, using the ECMA-182 polynomial,
	bit-reversed, with one's complement pre and post processing.
	Based on <PERSON>'s v1.4 implementation in C under the ZLIB license.
*/
@(optimization_mode="favor_size")
crc64_xz :: proc "contextless" (data: []byte, seed := u64(0)) -> u64 #no_bounds_check {
	data := data
	result := ~u64le(seed)

	// Process single bytes until aligned by 8
	#no_bounds_check for len(data) > 0 && uintptr(&data[0]) & uintptr(7) != 0 {
		result = _crc64_table_xz[0][(result ~ u64le(data[0])) & 0xff] ~ (result >> 8)
		data = data[1:]
	}

	// Process 8 bytes at a time.
	#no_bounds_check for len(data) >= 8 {
		b := (^u64le)(&data[0])^
		result ~= b
		result = _crc64_table_xz[7][ result & 0xff] ~
				 _crc64_table_xz[6][(result >> 8) & 0xff] ~
				 _crc64_table_xz[5][(result >> 16) & 0xff] ~
				 _crc64_table_xz[4][(result >> 24) & 0xff] ~
				 _crc64_table_xz[3][(result >> 32) & 0xff] ~
				 _crc64_table_xz[2][(result >> 40) & 0xff] ~
				 _crc64_table_xz[1][(result >> 48) & 0xff] ~
				 _crc64_table_xz[0][ result >> 56]
		data = data[8:]
	}

	// Process tail
	#no_bounds_check for len(data) > 0 {
		result = _crc64_table_xz[0][(result ~ u64le(data[0])) & 0xff] ~ (result >> 8)
		data = data[1:]
	}

	return u64(~result)
}

/*
	Generator polynomial: x^64 + x^4 + x^3 + x + 1
*/
@(optimization_mode="favor_size")
crc64_iso_3306 :: proc "contextless" (data: []byte, seed := u64(0)) -> u64 #no_bounds_check {

	result := seed

	for b in data {
		tmp_1h := result >> 40

		low    := (result & 0xFFFF_FFFF)
		shr    := ((result >> 32) & 0xFF) << 24
		tmp_1l := (low >> 8) | shr
		index  := (low ~ u64(b)) & 0xFF

		result  = (tmp_1h << 32 | tmp_1l) ~ (u64(_crc64_table_iso_3306[index]) << 48)
	}
	return result
}

crc64_iso_3306_inverse :: proc "contextless" (data: []byte, seed := u64(0)) -> u64 {
	result := #force_inline crc64_iso_3306(data, ~seed)
	return ~result
}

@private _crc64_table_ecma_182 := [256]u64{
	0x0000000000000000, 0x42f0e1eba9ea3693, 0x85e1c3d753d46d26, 0xc711223cfa3e5bb5,
	0x493366450e42ecdf, 0x0bc387aea7a8da4c, 0xccd2a5925d9681f9, 0x8e224479f47cb76a,
	0x9266cc8a1c85d9be, 0xd0962d61b56fef2d, 0x17870f5d4f51b498, 0x5577eeb6e6bb820b,
	0xdb55aacf12c73561, 0x99a54b24bb2d03f2, 0x5eb4691841135847, 0x1c4488f3e8f96ed4,
	0x663d78ff90e185ef, 0x24cd9914390bb37c, 0xe3dcbb28c335e8c9, 0xa12c5ac36adfde5a,
	0x2f0e1eba9ea36930, 0x6dfeff5137495fa3, 0xaaefdd6dcd770416, 0xe81f3c86649d3285,
	0xf45bb4758c645c51, 0xb6ab559e258e6ac2, 0x71ba77a2dfb03177, 0x334a9649765a07e4,
	0xbd68d2308226b08e, 0xff9833db2bcc861d, 0x388911e7d1f2dda8, 0x7a79f00c7818eb3b,
	0xcc7af1ff21c30bde, 0x8e8a101488293d4d, 0x499b3228721766f8, 0x0b6bd3c3dbfd506b,
	0x854997ba2f81e701, 0xc7b97651866bd192, 0x00a8546d7c558a27, 0x4258b586d5bfbcb4,
	0x5e1c3d753d46d260, 0x1cecdc9e94ace4f3, 0xdbfdfea26e92bf46, 0x990d1f49c77889d5,
	0x172f5b3033043ebf, 0x55dfbadb9aee082c, 0x92ce98e760d05399, 0xd03e790cc93a650a,
	0xaa478900b1228e31, 0xe8b768eb18c8b8a2, 0x2fa64ad7e2f6e317, 0x6d56ab3c4b1cd584,
	0xe374ef45bf6062ee, 0xa1840eae168a547d, 0x66952c92ecb40fc8, 0x2465cd79455e395b,
	0x3821458aada7578f, 0x7ad1a461044d611c, 0xbdc0865dfe733aa9, 0xff3067b657990c3a,
	0x711223cfa3e5bb50, 0x33e2c2240a0f8dc3, 0xf4f3e018f031d676, 0xb60301f359dbe0e5,
	0xda050215ea6c212f, 0x98f5e3fe438617bc, 0x5fe4c1c2b9b84c09, 0x1d14202910527a9a,
	0x93366450e42ecdf0, 0xd1c685bb4dc4fb63, 0x16d7a787b7faa0d6, 0x5427466c1e109645,
	0x4863ce9ff6e9f891, 0x0a932f745f03ce02, 0xcd820d48a53d95b7, 0x8f72eca30cd7a324,
	0x0150a8daf8ab144e, 0x43a04931514122dd, 0x84b16b0dab7f7968, 0xc6418ae602954ffb,
	0xbc387aea7a8da4c0, 0xfec89b01d3679253, 0x39d9b93d2959c9e6, 0x7b2958d680b3ff75,
	0xf50b1caf74cf481f, 0xb7fbfd44dd257e8c, 0x70eadf78271b2539, 0x321a3e938ef113aa,
	0x2e5eb66066087d7e, 0x6cae578bcfe24bed, 0xabbf75b735dc1058, 0xe94f945c9c3626cb,
	0x676dd025684a91a1, 0x259d31cec1a0a732, 0xe28c13f23b9efc87, 0xa07cf2199274ca14,
	0x167ff3eacbaf2af1, 0x548f120162451c62, 0x939e303d987b47d7, 0xd16ed1d631917144,
	0x5f4c95afc5edc62e, 0x1dbc74446c07f0bd, 0xdaad56789639ab08, 0x985db7933fd39d9b,
	0x84193f60d72af34f, 0xc6e9de8b7ec0c5dc, 0x01f8fcb784fe9e69, 0x43081d5c2d14a8fa,
	0xcd2a5925d9681f90, 0x8fdab8ce70822903, 0x48cb9af28abc72b6, 0x0a3b7b1923564425,
	0x70428b155b4eaf1e, 0x32b26afef2a4998d, 0xf5a348c2089ac238, 0xb753a929a170f4ab,
	0x3971ed50550c43c1, 0x7b810cbbfce67552, 0xbc902e8706d82ee7, 0xfe60cf6caf321874,
	0xe224479f47cb76a0, 0xa0d4a674ee214033, 0x67c58448141f1b86, 0x253565a3bdf52d15,
	0xab1721da49899a7f, 0xe9e7c031e063acec, 0x2ef6e20d1a5df759, 0x6c0603e6b3b7c1ca,
	0xf6fae5c07d3274cd, 0xb40a042bd4d8425e, 0x731b26172ee619eb, 0x31ebc7fc870c2f78,
	0xbfc9838573709812, 0xfd39626eda9aae81, 0x3a28405220a4f534, 0x78d8a1b9894ec3a7,
	0x649c294a61b7ad73, 0x266cc8a1c85d9be0, 0xe17dea9d3263c055, 0xa38d0b769b89f6c6,
	0x2daf4f0f6ff541ac, 0x6f5faee4c61f773f, 0xa84e8cd83c212c8a, 0xeabe6d3395cb1a19,
	0x90c79d3fedd3f122, 0xd2377cd44439c7b1, 0x15265ee8be079c04, 0x57d6bf0317edaa97,
	0xd9f4fb7ae3911dfd, 0x9b041a914a7b2b6e, 0x5c1538adb04570db, 0x1ee5d94619af4648,
	0x02a151b5f156289c, 0x4051b05e58bc1e0f, 0x87409262a28245ba, 0xc5b073890b687329,
	0x4b9237f0ff14c443, 0x0962d61b56fef2d0, 0xce73f427acc0a965, 0x8c8315cc052a9ff6,
	0x3a80143f5cf17f13, 0x7870f5d4f51b4980, 0xbf61d7e80f251235, 0xfd913603a6cf24a6,
	0x73b3727a52b393cc, 0x31439391fb59a55f, 0xf652b1ad0167feea, 0xb4a25046a88dc879,
	0xa8e6d8b54074a6ad, 0xea16395ee99e903e, 0x2d071b6213a0cb8b, 0x6ff7fa89ba4afd18,
	0xe1d5bef04e364a72, 0xa3255f1be7dc7ce1, 0x64347d271de22754, 0x26c49cccb40811c7,
	0x5cbd6cc0cc10fafc, 0x1e4d8d2b65facc6f, 0xd95caf179fc497da, 0x9bac4efc362ea149,
	0x158e0a85c2521623, 0x577eeb6e6bb820b0, 0x906fc95291867b05, 0xd29f28b9386c4d96,
	0xcedba04ad0952342, 0x8c2b41a1797f15d1, 0x4b3a639d83414e64, 0x09ca82762aab78f7,
	0x87e8c60fded7cf9d, 0xc51827e4773df90e, 0x020905d88d03a2bb, 0x40f9e43324e99428,
	0x2cffe7d5975e55e2, 0x6e0f063e3eb46371, 0xa91e2402c48a38c4, 0xebeec5e96d600e57,
	0x65cc8190991cb93d, 0x273c607b30f68fae, 0xe02d4247cac8d41b, 0xa2dda3ac6322e288,
	0xbe992b5f8bdb8c5c, 0xfc69cab42231bacf, 0x3b78e888d80fe17a, 0x7988096371e5d7e9,
	0xf7aa4d1a85996083, 0xb55aacf12c735610, 0x724b8ecdd64d0da5, 0x30bb6f267fa73b36,
	0x4ac29f2a07bfd00d, 0x08327ec1ae55e69e, 0xcf235cfd546bbd2b, 0x8dd3bd16fd818bb8,
	0x03f1f96f09fd3cd2, 0x41011884a0170a41, 0x86103ab85a2951f4, 0xc4e0db53f3c36767,
	0xd8a453a01b3a09b3, 0x9a54b24bb2d03f20, 0x5d45907748ee6495, 0x1fb5719ce1045206,
	0x919735e51578e56c, 0xd367d40ebc92d3ff, 0x1476f63246ac884a, 0x568617d9ef46bed9,
	0xe085162ab69d5e3c, 0xa275f7c11f7768af, 0x6564d5fde549331a, 0x279434164ca30589,
	0xa9b6706fb8dfb2e3, 0xeb46918411358470, 0x2c57b3b8eb0bdfc5, 0x6ea7525342e1e956,
	0x72e3daa0aa188782, 0x30133b4b03f2b111, 0xf7021977f9cceaa4, 0xb5f2f89c5026dc37,
	0x3bd0bce5a45a6b5d, 0x79205d0e0db05dce, 0xbe317f32f78e067b, 0xfcc19ed95e6430e8,
	0x86b86ed5267cdbd3, 0xc4488f3e8f96ed40, 0x0359ad0275a8b6f5, 0x41a94ce9dc428066,
	0xcf8b0890283e370c, 0x8d7be97b81d4019f, 0x4a6acb477bea5a2a, 0x089a2aacd2006cb9,
	0x14dea25f3af9026d, 0x562e43b4931334fe, 0x913f6188692d6f4b, 0xd3cf8063c0c759d8,
	0x5dedc41a34bbeeb2, 0x1f1d25f19d51d821, 0xd80c07cd676f8394, 0x9afce626ce85b507,
}

@private _crc64_table_xz := [8][256]u64le{
	{	
		0x0000000000000000, 0xb32e4cbe03a75f6f, 0xf4843657a840a05b, 0x47aa7ae9abe7ff34, 
		0x7bd0c384ff8f5e33, 0xc8fe8f3afc28015c, 0x8f54f5d357cffe68, 0x3c7ab96d5468a107, 
		0xf7a18709ff1ebc66, 0x448fcbb7fcb9e309, 0x0325b15e575e1c3d, 0xb00bfde054f94352, 
		0x8c71448d0091e255, 0x3f5f08330336bd3a, 0x78f572daa8d1420e, 0xcbdb3e64ab761d61, 
		0x7d9ba13851336649, 0xceb5ed8652943926, 0x891f976ff973c612, 0x3a31dbd1fad4997d, 
		0x064b62bcaebc387a, 0xb5652e02ad1b6715, 0xf2cf54eb06fc9821, 0x41e11855055bc74e, 
		0x8a3a2631ae2dda2f, 0x39146a8fad8a8540, 0x7ebe1066066d7a74, 0xcd905cd805ca251b, 
		0xf1eae5b551a2841c, 0x42c4a90b5205db73, 0x056ed3e2f9e22447, 0xb6409f5cfa457b28, 
		0xfb374270a266cc92, 0x48190ecea1c193fd, 0x0fb374270a266cc9, 0xbc9d3899098133a6, 
		0x80e781f45de992a1, 0x33c9cd4a5e4ecdce, 0x7463b7a3f5a932fa, 0xc74dfb1df60e6d95, 
		0x0c96c5795d7870f4, 0xbfb889c75edf2f9b, 0xf812f32ef538d0af, 0x4b3cbf90f69f8fc0, 
		0x774606fda2f72ec7, 0xc4684a43a15071a8, 0x83c230aa0ab78e9c, 0x30ec7c140910d1f3, 
		0x86ace348f355aadb, 0x3582aff6f0f2f5b4, 0x7228d51f5b150a80, 0xc10699a158b255ef, 
		0xfd7c20cc0cdaf4e8, 0x4e526c720f7dab87, 0x09f8169ba49a54b3, 0xbad65a25a73d0bdc, 
		0x710d64410c4b16bd, 0xc22328ff0fec49d2, 0x85895216a40bb6e6, 0x36a71ea8a7ace989, 
		0x0adda7c5f3c4488e, 0xb9f3eb7bf06317e1, 0xfe5991925b84e8d5, 0x4d77dd2c5823b7ba, 
		0x64b62bcaebc387a1, 0xd7986774e864d8ce, 0x90321d9d438327fa, 0x231c512340247895, 
		0x1f66e84e144cd992, 0xac48a4f017eb86fd, 0xebe2de19bc0c79c9, 0x58cc92a7bfab26a6, 
		0x9317acc314dd3bc7, 0x2039e07d177a64a8, 0x67939a94bc9d9b9c, 0xd4bdd62abf3ac4f3, 
		0xe8c76f47eb5265f4, 0x5be923f9e8f53a9b, 0x1c4359104312c5af, 0xaf6d15ae40b59ac0, 
		0x192d8af2baf0e1e8, 0xaa03c64cb957be87, 0xeda9bca512b041b3, 0x5e87f01b11171edc, 
		0x62fd4976457fbfdb, 0xd1d305c846d8e0b4, 0x96797f21ed3f1f80, 0x2557339fee9840ef, 
		0xee8c0dfb45ee5d8e, 0x5da24145464902e1, 0x1a083bacedaefdd5, 0xa9267712ee09a2ba, 
		0x955cce7fba6103bd, 0x267282c1b9c65cd2, 0x61d8f8281221a3e6, 0xd2f6b4961186fc89, 
		0x9f8169ba49a54b33, 0x2caf25044a02145c, 0x6b055fede1e5eb68, 0xd82b1353e242b407, 
		0xe451aa3eb62a1500, 0x577fe680b58d4a6f, 0x10d59c691e6ab55b, 0xa3fbd0d71dcdea34, 
		0x6820eeb3b6bbf755, 0xdb0ea20db51ca83a, 0x9ca4d8e41efb570e, 0x2f8a945a1d5c0861, 
		0x13f02d374934a966, 0xa0de61894a93f609, 0xe7741b60e174093d, 0x545a57dee2d35652, 
		0xe21ac88218962d7a, 0x5134843c1b317215, 0x169efed5b0d68d21, 0xa5b0b26bb371d24e, 
		0x99ca0b06e7197349, 0x2ae447b8e4be2c26, 0x6d4e3d514f59d312, 0xde6071ef4cfe8c7d, 
		0x15bb4f8be788911c, 0xa6950335e42fce73, 0xe13f79dc4fc83147, 0x521135624c6f6e28, 
		0x6e6b8c0f1807cf2f, 0xdd45c0b11ba09040, 0x9aefba58b0476f74, 0x29c1f6e6b3e0301b, 
		0xc96c5795d7870f42, 0x7a421b2bd420502d, 0x3de861c27fc7af19, 0x8ec62d7c7c60f076, 
		0xb2bc941128085171, 0x0192d8af2baf0e1e, 0x4638a2468048f12a, 0xf516eef883efae45, 
		0x3ecdd09c2899b324, 0x8de39c222b3eec4b, 0xca49e6cb80d9137f, 0x7967aa75837e4c10, 
		0x451d1318d716ed17, 0xf6335fa6d4b1b278, 0xb199254f7f564d4c, 0x02b769f17cf11223, 
		0xb4f7f6ad86b4690b, 0x07d9ba1385133664, 0x4073c0fa2ef4c950, 0xf35d8c442d53963f, 
		0xcf273529793b3738, 0x7c0979977a9c6857, 0x3ba3037ed17b9763, 0x888d4fc0d2dcc80c, 
		0x435671a479aad56d, 0xf0783d1a7a0d8a02, 0xb7d247f3d1ea7536, 0x04fc0b4dd24d2a59, 
		0x3886b22086258b5e, 0x8ba8fe9e8582d431, 0xcc0284772e652b05, 0x7f2cc8c92dc2746a, 
		0x325b15e575e1c3d0, 0x8175595b76469cbf, 0xc6df23b2dda1638b, 0x75f16f0cde063ce4, 
		0x498bd6618a6e9de3, 0xfaa59adf89c9c28c, 0xbd0fe036222e3db8, 0x0e21ac88218962d7, 
		0xc5fa92ec8aff7fb6, 0x76d4de52895820d9, 0x317ea4bb22bfdfed, 0x8250e80521188082, 
		0xbe2a516875702185, 0x0d041dd676d77eea, 0x4aae673fdd3081de, 0xf9802b81de97deb1, 
		0x4fc0b4dd24d2a599, 0xfceef8632775faf6, 0xbb44828a8c9205c2, 0x086ace348f355aad, 
		0x34107759db5dfbaa, 0x873e3be7d8faa4c5, 0xc094410e731d5bf1, 0x73ba0db070ba049e, 
		0xb86133d4dbcc19ff, 0x0b4f7f6ad86b4690, 0x4ce50583738cb9a4, 0xffcb493d702be6cb, 
		0xc3b1f050244347cc, 0x709fbcee27e418a3, 0x3735c6078c03e797, 0x841b8ab98fa4b8f8, 
		0xadda7c5f3c4488e3, 0x1ef430e13fe3d78c, 0x595e4a08940428b8, 0xea7006b697a377d7, 
		0xd60abfdbc3cbd6d0, 0x6524f365c06c89bf, 0x228e898c6b8b768b, 0x91a0c532682c29e4, 
		0x5a7bfb56c35a3485, 0xe955b7e8c0fd6bea, 0xaeffcd016b1a94de, 0x1dd181bf68bdcbb1, 
		0x21ab38d23cd56ab6, 0x9285746c3f7235d9, 0xd52f0e859495caed, 0x6601423b97329582, 
		0xd041dd676d77eeaa, 0x636f91d96ed0b1c5, 0x24c5eb30c5374ef1, 0x97eba78ec690119e, 
		0xab911ee392f8b099, 0x18bf525d915feff6, 0x5f1528b43ab810c2, 0xec3b640a391f4fad, 
		0x27e05a6e926952cc, 0x94ce16d091ce0da3, 0xd3646c393a29f297, 0x604a2087398eadf8, 
		0x5c3099ea6de60cff, 0xef1ed5546e415390, 0xa8b4afbdc5a6aca4, 0x1b9ae303c601f3cb, 
		0x56ed3e2f9e224471, 0xe5c372919d851b1e, 0xa26908783662e42a, 0x114744c635c5bb45, 
		0x2d3dfdab61ad1a42, 0x9e13b115620a452d, 0xd9b9cbfcc9edba19, 0x6a978742ca4ae576, 
		0xa14cb926613cf817, 0x1262f598629ba778, 0x55c88f71c97c584c, 0xe6e6c3cfcadb0723, 
		0xda9c7aa29eb3a624, 0x69b2361c9d14f94b, 0x2e184cf536f3067f, 0x9d36004b35545910, 
		0x2b769f17cf112238, 0x9858d3a9ccb67d57, 0xdff2a94067518263, 0x6cdce5fe64f6dd0c, 
		0x50a65c93309e7c0b, 0xe388102d33392364, 0xa4226ac498dedc50, 0x170c267a9b79833f, 
		0xdcd7181e300f9e5e, 0x6ff954a033a8c131, 0x28532e49984f3e05, 0x9b7d62f79be8616a, 
		0xa707db9acf80c06d, 0x14299724cc279f02, 0x5383edcd67c06036, 0xe0ada17364673f59,
	},
	{	
		0x0000000000000000, 0x54e979925cd0f10d, 0xa9d2f324b9a1e21a, 0xfd3b8ab6e5711317, 
		0xc17d4962dc4ddab1, 0x959430f0809d2bbc, 0x68afba4665ec38ab, 0x3c46c3d4393cc9a6, 
		0x10223dee1795abe7, 0x44cb447c4b455aea, 0xb9f0cecaae3449fd, 0xed19b758f2e4b8f0, 
		0xd15f748ccbd87156, 0x85b60d1e9708805b, 0x788d87a87279934c, 0x2c64fe3a2ea96241, 
		0x20447bdc2f2b57ce, 0x74ad024e73fba6c3, 0x899688f8968ab5d4, 0xdd7ff16aca5a44d9, 
		0xe13932bef3668d7f, 0xb5d04b2cafb67c72, 0x48ebc19a4ac76f65, 0x1c02b80816179e68, 
		0x3066463238befc29, 0x648f3fa0646e0d24, 0x99b4b516811f1e33, 0xcd5dcc84ddcfef3e, 
		0xf11b0f50e4f32698, 0xa5f276c2b823d795, 0x58c9fc745d52c482, 0x0c2085e60182358f, 
		0x4088f7b85e56af9c, 0x14618e2a02865e91, 0xe95a049ce7f74d86, 0xbdb37d0ebb27bc8b, 
		0x81f5beda821b752d, 0xd51cc748decb8420, 0x28274dfe3bba9737, 0x7cce346c676a663a, 
		0x50aaca5649c3047b, 0x0443b3c41513f576, 0xf9783972f062e661, 0xad9140e0acb2176c, 
		0x91d78334958edeca, 0xc53efaa6c95e2fc7, 0x380570102c2f3cd0, 0x6cec098270ffcddd, 
		0x60cc8c64717df852, 0x3425f5f62dad095f, 0xc91e7f40c8dc1a48, 0x9df706d2940ceb45, 
		0xa1b1c506ad3022e3, 0xf558bc94f1e0d3ee, 0x086336221491c0f9, 0x5c8a4fb0484131f4, 
		0x70eeb18a66e853b5, 0x2407c8183a38a2b8, 0xd93c42aedf49b1af, 0x8dd53b3c839940a2, 
		0xb193f8e8baa58904, 0xe57a817ae6757809, 0x18410bcc03046b1e, 0x4ca8725e5fd49a13, 
		0x8111ef70bcad5f38, 0xd5f896e2e07dae35, 0x28c31c54050cbd22, 0x7c2a65c659dc4c2f, 
		0x406ca61260e08589, 0x1485df803c307484, 0xe9be5536d9416793, 0xbd572ca48591969e, 
		0x9133d29eab38f4df, 0xc5daab0cf7e805d2, 0x38e121ba129916c5, 0x6c0858284e49e7c8, 
		0x504e9bfc77752e6e, 0x04a7e26e2ba5df63, 0xf99c68d8ced4cc74, 0xad75114a92043d79, 
		0xa15594ac938608f6, 0xf5bced3ecf56f9fb, 0x088767882a27eaec, 0x5c6e1e1a76f71be1, 
		0x6028ddce4fcbd247, 0x34c1a45c131b234a, 0xc9fa2eeaf66a305d, 0x9d135778aabac150, 
		0xb177a9428413a311, 0xe59ed0d0d8c3521c, 0x18a55a663db2410b, 0x4c4c23f46162b006, 
		0x700ae020585e79a0, 0x24e399b2048e88ad, 0xd9d81304e1ff9bba, 0x8d316a96bd2f6ab7, 
		0xc19918c8e2fbf0a4, 0x9570615abe2b01a9, 0x684bebec5b5a12be, 0x3ca2927e078ae3b3, 
		0x00e451aa3eb62a15, 0x540d28386266db18, 0xa936a28e8717c80f, 0xfddfdb1cdbc73902, 
		0xd1bb2526f56e5b43, 0x85525cb4a9beaa4e, 0x7869d6024ccfb959, 0x2c80af90101f4854, 
		0x10c66c44292381f2, 0x442f15d675f370ff, 0xb9149f60908263e8, 0xedfde6f2cc5292e5, 
		0xe1dd6314cdd0a76a, 0xb5341a8691005667, 0x480f903074714570, 0x1ce6e9a228a1b47d, 
		0x20a02a76119d7ddb, 0x744953e44d4d8cd6, 0x8972d952a83c9fc1, 0xdd9ba0c0f4ec6ecc, 
		0xf1ff5efada450c8d, 0xa51627688695fd80, 0x582dadde63e4ee97, 0x0cc4d44c3f341f9a, 
		0x308217980608d63c, 0x646b6e0a5ad82731, 0x9950e4bcbfa93426, 0xcdb99d2ee379c52b, 
		0x90fb71cad654a0f5, 0xc41208588a8451f8, 0x392982ee6ff542ef, 0x6dc0fb7c3325b3e2, 
		0x518638a80a197a44, 0x056f413a56c98b49, 0xf854cb8cb3b8985e, 0xacbdb21eef686953, 
		0x80d94c24c1c10b12, 0xd43035b69d11fa1f, 0x290bbf007860e908, 0x7de2c69224b01805, 
		0x41a405461d8cd1a3, 0x154d7cd4415c20ae, 0xe876f662a42d33b9, 0xbc9f8ff0f8fdc2b4, 
		0xb0bf0a16f97ff73b, 0xe4567384a5af0636, 0x196df93240de1521, 0x4d8480a01c0ee42c, 
		0x71c2437425322d8a, 0x252b3ae679e2dc87, 0xd810b0509c93cf90, 0x8cf9c9c2c0433e9d, 
		0xa09d37f8eeea5cdc, 0xf4744e6ab23aadd1, 0x094fc4dc574bbec6, 0x5da6bd4e0b9b4fcb, 
		0x61e07e9a32a7866d, 0x350907086e777760, 0xc8328dbe8b066477, 0x9cdbf42cd7d6957a, 
		0xd073867288020f69, 0x849affe0d4d2fe64, 0x79a1755631a3ed73, 0x2d480cc46d731c7e, 
		0x110ecf10544fd5d8, 0x45e7b682089f24d5, 0xb8dc3c34edee37c2, 0xec3545a6b13ec6cf, 
		0xc051bb9c9f97a48e, 0x94b8c20ec3475583, 0x698348b826364694, 0x3d6a312a7ae6b799, 
		0x012cf2fe43da7e3f, 0x55c58b6c1f0a8f32, 0xa8fe01dafa7b9c25, 0xfc177848a6ab6d28, 
		0xf037fdaea72958a7, 0xa4de843cfbf9a9aa, 0x59e50e8a1e88babd, 0x0d0c771842584bb0, 
		0x314ab4cc7b648216, 0x65a3cd5e27b4731b, 0x989847e8c2c5600c, 0xcc713e7a9e159101, 
		0xe015c040b0bcf340, 0xb4fcb9d2ec6c024d, 0x49c73364091d115a, 0x1d2e4af655cde057, 
		0x216889226cf129f1, 0x7581f0b03021d8fc, 0x88ba7a06d550cbeb, 0xdc53039489803ae6, 
		0x11ea9eba6af9ffcd, 0x4503e72836290ec0, 0xb8386d9ed3581dd7, 0xecd1140c8f88ecda, 
		0xd097d7d8b6b4257c, 0x847eae4aea64d471, 0x794524fc0f15c766, 0x2dac5d6e53c5366b, 
		0x01c8a3547d6c542a, 0x5521dac621bca527, 0xa81a5070c4cdb630, 0xfcf329e2981d473d, 
		0xc0b5ea36a1218e9b, 0x945c93a4fdf17f96, 0x6967191218806c81, 0x3d8e608044509d8c, 
		0x31aee56645d2a803, 0x65479cf41902590e, 0x987c1642fc734a19, 0xcc956fd0a0a3bb14, 
		0xf0d3ac04999f72b2, 0xa43ad596c54f83bf, 0x59015f20203e90a8, 0x0de826b27cee61a5, 
		0x218cd888524703e4, 0x7565a11a0e97f2e9, 0x885e2bacebe6e1fe, 0xdcb7523eb73610f3, 
		0xe0f191ea8e0ad955, 0xb418e878d2da2858, 0x492362ce37ab3b4f, 0x1dca1b5c6b7bca42, 
		0x5162690234af5051, 0x058b1090687fa15c, 0xf8b09a268d0eb24b, 0xac59e3b4d1de4346, 
		0x901f2060e8e28ae0, 0xc4f659f2b4327bed, 0x39cdd344514368fa, 0x6d24aad60d9399f7, 
		0x414054ec233afbb6, 0x15a92d7e7fea0abb, 0xe892a7c89a9b19ac, 0xbc7bde5ac64be8a1, 
		0x803d1d8eff772107, 0xd4d4641ca3a7d00a, 0x29efeeaa46d6c31d, 0x7d0697381a063210, 
		0x712612de1b84079f, 0x25cf6b4c4754f692, 0xd8f4e1faa225e585, 0x8c1d9868fef51488, 
		0xb05b5bbcc7c9dd2e, 0xe4b2222e9b192c23, 0x1989a8987e683f34, 0x4d60d10a22b8ce39, 
		0x61042f300c11ac78, 0x35ed56a250c15d75, 0xc8d6dc14b5b04e62, 0x9c3fa586e960bf6f, 
		0xa0796652d05c76c9, 0xf4901fc08c8c87c4, 0x09ab957669fd94d3, 0x5d42ece4352d65de,
	},
	{	
		0x0000000000000000, 0x3f0be14a916a6dcb, 0x7e17c29522d4db96, 0x411c23dfb3beb65d, 
		0xfc2f852a45a9b72c, 0xc3246460d4c3dae7, 0x823847bf677d6cba, 0xbd33a6f5f6170171, 
		0x6a87a57f245d70dd, 0x558c4435b5371d16, 0x149067ea0689ab4b, 0x2b9b86a097e3c680, 
		0x96a8205561f4c7f1, 0xa9a3c11ff09eaa3a, 0xe8bfe2c043201c67, 0xd7b4038ad24a71ac, 
		0xd50f4afe48bae1ba, 0xea04abb4d9d08c71, 0xab18886b6a6e3a2c, 0x94136921fb0457e7, 
		0x2920cfd40d135696, 0x162b2e9e9c793b5d, 0x57370d412fc78d00, 0x683cec0bbeade0cb, 
		0xbf88ef816ce79167, 0x80830ecbfd8dfcac, 0xc19f2d144e334af1, 0xfe94cc5edf59273a, 
		0x43a76aab294e264b, 0x7cac8be1b8244b80, 0x3db0a83e0b9afddd, 0x02bb49749af09016, 
		0x38c63ad73e7bddf1, 0x07cddb9daf11b03a, 0x46d1f8421caf0667, 0x79da19088dc56bac, 
		0xc4e9bffd7bd26add, 0xfbe25eb7eab80716, 0xbafe7d685906b14b, 0x85f59c22c86cdc80, 
		0x52419fa81a26ad2c, 0x6d4a7ee28b4cc0e7, 0x2c565d3d38f276ba, 0x135dbc77a9981b71, 
		0xae6e1a825f8f1a00, 0x9165fbc8cee577cb, 0xd079d8177d5bc196, 0xef72395dec31ac5d, 
		0xedc9702976c13c4b, 0xd2c29163e7ab5180, 0x93deb2bc5415e7dd, 0xacd553f6c57f8a16, 
		0x11e6f50333688b67, 0x2eed1449a202e6ac, 0x6ff1379611bc50f1, 0x50fad6dc80d63d3a, 
		0x874ed556529c4c96, 0xb845341cc3f6215d, 0xf95917c370489700, 0xc652f689e122facb, 
		0x7b61507c1735fbba, 0x446ab136865f9671, 0x057692e935e1202c, 0x3a7d73a3a48b4de7, 
		0x718c75ae7cf7bbe2, 0x4e8794e4ed9dd629, 0x0f9bb73b5e236074, 0x30905671cf490dbf, 
		0x8da3f084395e0cce, 0xb2a811cea8346105, 0xf3b432111b8ad758, 0xccbfd35b8ae0ba93, 
		0x1b0bd0d158aacb3f, 0x2400319bc9c0a6f4, 0x651c12447a7e10a9, 0x5a17f30eeb147d62, 
		0xe72455fb1d037c13, 0xd82fb4b18c6911d8, 0x9933976e3fd7a785, 0xa6387624aebdca4e, 
		0xa4833f50344d5a58, 0x9b88de1aa5273793, 0xda94fdc5169981ce, 0xe59f1c8f87f3ec05, 
		0x58acba7a71e4ed74, 0x67a75b30e08e80bf, 0x26bb78ef533036e2, 0x19b099a5c25a5b29, 
		0xce049a2f10102a85, 0xf10f7b65817a474e, 0xb01358ba32c4f113, 0x8f18b9f0a3ae9cd8, 
		0x322b1f0555b99da9, 0x0d20fe4fc4d3f062, 0x4c3cdd90776d463f, 0x73373cdae6072bf4, 
		0x494a4f79428c6613, 0x7641ae33d3e60bd8, 0x375d8dec6058bd85, 0x08566ca6f132d04e, 
		0xb565ca530725d13f, 0x8a6e2b19964fbcf4, 0xcb7208c625f10aa9, 0xf479e98cb49b6762, 
		0x23cdea0666d116ce, 0x1cc60b4cf7bb7b05, 0x5dda28934405cd58, 0x62d1c9d9d56fa093, 
		0xdfe26f2c2378a1e2, 0xe0e98e66b212cc29, 0xa1f5adb901ac7a74, 0x9efe4cf390c617bf, 
		0x9c4505870a3687a9, 0xa34ee4cd9b5cea62, 0xe252c71228e25c3f, 0xdd592658b98831f4, 
		0x606a80ad4f9f3085, 0x5f6161e7def55d4e, 0x1e7d42386d4beb13, 0x2176a372fc2186d8, 
		0xf6c2a0f82e6bf774, 0xc9c941b2bf019abf, 0x88d5626d0cbf2ce2, 0xb7de83279dd54129, 
		0x0aed25d26bc24058, 0x35e6c498faa82d93, 0x74fae74749169bce, 0x4bf1060dd87cf605, 
		0xe318eb5cf9ef77c4, 0xdc130a1668851a0f, 0x9d0f29c9db3bac52, 0xa204c8834a51c199, 
		0x1f376e76bc46c0e8, 0x203c8f3c2d2cad23, 0x6120ace39e921b7e, 0x5e2b4da90ff876b5, 
		0x899f4e23ddb20719, 0xb694af694cd86ad2, 0xf7888cb6ff66dc8f, 0xc8836dfc6e0cb144, 
		0x75b0cb09981bb035, 0x4abb2a430971ddfe, 0x0ba7099cbacf6ba3, 0x34ace8d62ba50668, 
		0x3617a1a2b155967e, 0x091c40e8203ffbb5, 0x4800633793814de8, 0x770b827d02eb2023, 
		0xca382488f4fc2152, 0xf533c5c265964c99, 0xb42fe61dd628fac4, 0x8b2407574742970f, 
		0x5c9004dd9508e6a3, 0x639be59704628b68, 0x2287c648b7dc3d35, 0x1d8c270226b650fe, 
		0xa0bf81f7d0a1518f, 0x9fb460bd41cb3c44, 0xdea84362f2758a19, 0xe1a3a228631fe7d2, 
		0xdbded18bc794aa35, 0xe4d530c156fec7fe, 0xa5c9131ee54071a3, 0x9ac2f254742a1c68, 
		0x27f154a1823d1d19, 0x18fab5eb135770d2, 0x59e69634a0e9c68f, 0x66ed777e3183ab44, 
		0xb15974f4e3c9dae8, 0x8e5295be72a3b723, 0xcf4eb661c11d017e, 0xf045572b50776cb5, 
		0x4d76f1dea6606dc4, 0x727d1094370a000f, 0x3361334b84b4b652, 0x0c6ad20115dedb99, 
		0x0ed19b758f2e4b8f, 0x31da7a3f1e442644, 0x70c659e0adfa9019, 0x4fcdb8aa3c90fdd2, 
		0xf2fe1e5fca87fca3, 0xcdf5ff155bed9168, 0x8ce9dccae8532735, 0xb3e23d8079394afe, 
		0x64563e0aab733b52, 0x5b5ddf403a195699, 0x1a41fc9f89a7e0c4, 0x254a1dd518cd8d0f, 
		0x9879bb20eeda8c7e, 0xa7725a6a7fb0e1b5, 0xe66e79b5cc0e57e8, 0xd96598ff5d643a23, 
		0x92949ef28518cc26, 0xad9f7fb81472a1ed, 0xec835c67a7cc17b0, 0xd388bd2d36a67a7b, 
		0x6ebb1bd8c0b17b0a, 0x51b0fa9251db16c1, 0x10acd94de265a09c, 0x2fa73807730fcd57, 
		0xf8133b8da145bcfb, 0xc718dac7302fd130, 0x8604f9188391676d, 0xb90f185212fb0aa6, 
		0x043cbea7e4ec0bd7, 0x3b375fed7586661c, 0x7a2b7c32c638d041, 0x45209d785752bd8a, 
		0x479bd40ccda22d9c, 0x789035465cc84057, 0x398c1699ef76f60a, 0x0687f7d37e1c9bc1, 
		0xbbb45126880b9ab0, 0x84bfb06c1961f77b, 0xc5a393b3aadf4126, 0xfaa872f93bb52ced, 
		0x2d1c7173e9ff5d41, 0x121790397895308a, 0x530bb3e6cb2b86d7, 0x6c0052ac5a41eb1c, 
		0xd133f459ac56ea6d, 0xee3815133d3c87a6, 0xaf2436cc8e8231fb, 0x902fd7861fe85c30, 
		0xaa52a425bb6311d7, 0x9559456f2a097c1c, 0xd44566b099b7ca41, 0xeb4e87fa08dda78a, 
		0x567d210ffecaa6fb, 0x6976c0456fa0cb30, 0x286ae39adc1e7d6d, 0x176102d04d7410a6, 
		0xc0d5015a9f3e610a, 0xffdee0100e540cc1, 0xbec2c3cfbdeaba9c, 0x81c922852c80d757, 
		0x3cfa8470da97d626, 0x03f1653a4bfdbbed, 0x42ed46e5f8430db0, 0x7de6a7af6929607b, 
		0x7f5deedbf3d9f06d, 0x40560f9162b39da6, 0x014a2c4ed10d2bfb, 0x3e41cd0440674630, 
		0x83726bf1b6704741, 0xbc798abb271a2a8a, 0xfd65a96494a49cd7, 0xc26e482e05cef11c, 
		0x15da4ba4d78480b0, 0x2ad1aaee46eeed7b, 0x6bcd8931f5505b26, 0x54c6687b643a36ed, 
		0xe9f5ce8e922d379c, 0xd6fe2fc403475a57, 0x97e20c1bb0f9ec0a, 0xa8e9ed51219381c1,
	},
	{	
		0x0000000000000000, 0x1dee8a5e222ca1dc, 0x3bdd14bc445943b8, 0x26339ee26675e264, 
		0x77ba297888b28770, 0x6a54a326aa9e26ac, 0x4c673dc4ccebc4c8, 0x5189b79aeec76514, 
		0xef7452f111650ee0, 0xf29ad8af3349af3c, 0xd4a9464d553c4d58, 0xc947cc137710ec84, 
		0x98ce7b8999d78990, 0x8520f1d7bbfb284c, 0xa3136f35dd8eca28, 0xbefde56bffa26bf4, 
		0x4c300ac98dc40345, 0x51de8097afe8a299, 0x77ed1e75c99d40fd, 0x6a03942bebb1e121, 
		0x3b8a23b105768435, 0x2664a9ef275a25e9, 0x0057370d412fc78d, 0x1db9bd5363036651, 
		0xa34458389ca10da5, 0xbeaad266be8dac79, 0x98994c84d8f84e1d, 0x8577c6dafad4efc1, 
		0xd4fe714014138ad5, 0xc910fb1e363f2b09, 0xef2365fc504ac96d, 0xf2cdefa2726668b1, 
		0x986015931b88068a, 0x858e9fcd39a4a756, 0xa3bd012f5fd14532, 0xbe538b717dfde4ee, 
		0xefda3ceb933a81fa, 0xf234b6b5b1162026, 0xd4072857d763c242, 0xc9e9a209f54f639e, 
		0x771447620aed086a, 0x6afacd3c28c1a9b6, 0x4cc953de4eb44bd2, 0x5127d9806c98ea0e, 
		0x00ae6e1a825f8f1a, 0x1d40e444a0732ec6, 0x3b737aa6c606cca2, 0x269df0f8e42a6d7e, 
		0xd4501f5a964c05cf, 0xc9be9504b460a413, 0xef8d0be6d2154677, 0xf26381b8f039e7ab, 
		0xa3ea36221efe82bf, 0xbe04bc7c3cd22363, 0x9837229e5aa7c107, 0x85d9a8c0788b60db, 
		0x3b244dab87290b2f, 0x26cac7f5a505aaf3, 0x00f95917c3704897, 0x1d17d349e15ce94b, 
		0x4c9e64d30f9b8c5f, 0x5170ee8d2db72d83, 0x7743706f4bc2cfe7, 0x6aadfa3169ee6e3b, 
		0xa218840d981e1391, 0xbff60e53ba32b24d, 0x99c590b1dc475029, 0x842b1aeffe6bf1f5, 
		0xd5a2ad7510ac94e1, 0xc84c272b3280353d, 0xee7fb9c954f5d759, 0xf391339776d97685, 
		0x4d6cd6fc897b1d71, 0x50825ca2ab57bcad, 0x76b1c240cd225ec9, 0x6b5f481eef0eff15, 
		0x3ad6ff8401c99a01, 0x273875da23e53bdd, 0x010beb384590d9b9, 0x1ce5616667bc7865, 
		0xee288ec415da10d4, 0xf3c6049a37f6b108, 0xd5f59a785183536c, 0xc81b102673aff2b0, 
		0x9992a7bc9d6897a4, 0x847c2de2bf443678, 0xa24fb300d931d41c, 0xbfa1395efb1d75c0, 
		0x015cdc3504bf1e34, 0x1cb2566b2693bfe8, 0x3a81c88940e65d8c, 0x276f42d762cafc50, 
		0x76e6f54d8c0d9944, 0x6b087f13ae213898, 0x4d3be1f1c854dafc, 0x50d56bafea787b20, 
		0x3a78919e8396151b, 0x27961bc0a1bab4c7, 0x01a58522c7cf56a3, 0x1c4b0f7ce5e3f77f, 
		0x4dc2b8e60b24926b, 0x502c32b8290833b7, 0x761fac5a4f7dd1d3, 0x6bf126046d51700f, 
		0xd50cc36f92f31bfb, 0xc8e24931b0dfba27, 0xeed1d7d3d6aa5843, 0xf33f5d8df486f99f, 
		0xa2b6ea171a419c8b, 0xbf586049386d3d57, 0x996bfeab5e18df33, 0x848574f57c347eef, 
		0x76489b570e52165e, 0x6ba611092c7eb782, 0x4d958feb4a0b55e6, 0x507b05b56827f43a, 
		0x01f2b22f86e0912e, 0x1c1c3871a4cc30f2, 0x3a2fa693c2b9d296, 0x27c12ccde095734a, 
		0x993cc9a61f3718be, 0x84d243f83d1bb962, 0xa2e1dd1a5b6e5b06, 0xbf0f57447942fada, 
		0xee86e0de97859fce, 0xf3686a80b5a93e12, 0xd55bf462d3dcdc76, 0xc8b57e3cf1f07daa, 
		0xd6e9a7309f3239a7, 0xcb072d6ebd1e987b, 0xed34b38cdb6b7a1f, 0xf0da39d2f947dbc3, 
		0xa1538e481780bed7, 0xbcbd041635ac1f0b, 0x9a8e9af453d9fd6f, 0x876010aa71f55cb3, 
		0x399df5c18e573747, 0x24737f9fac7b969b, 0x0240e17dca0e74ff, 0x1fae6b23e822d523, 
		0x4e27dcb906e5b037, 0x53c956e724c911eb, 0x75fac80542bcf38f, 0x6814425b60905253, 
		0x9ad9adf912f63ae2, 0x873727a730da9b3e, 0xa104b94556af795a, 0xbcea331b7483d886, 
		0xed6384819a44bd92, 0xf08d0edfb8681c4e, 0xd6be903dde1dfe2a, 0xcb501a63fc315ff6, 
		0x75adff0803933402, 0x6843755621bf95de, 0x4e70ebb447ca77ba, 0x539e61ea65e6d666, 
		0x0217d6708b21b372, 0x1ff95c2ea90d12ae, 0x39cac2cccf78f0ca, 0x24244892ed545116, 
		0x4e89b2a384ba3f2d, 0x536738fda6969ef1, 0x7554a61fc0e37c95, 0x68ba2c41e2cfdd49, 
		0x39339bdb0c08b85d, 0x24dd11852e241981, 0x02ee8f674851fbe5, 0x1f0005396a7d5a39, 
		0xa1fde05295df31cd, 0xbc136a0cb7f39011, 0x9a20f4eed1867275, 0x87ce7eb0f3aad3a9, 
		0xd647c92a1d6db6bd, 0xcba943743f411761, 0xed9add965934f505, 0xf07457c87b1854d9, 
		0x02b9b86a097e3c68, 0x1f5732342b529db4, 0x3964acd64d277fd0, 0x248a26886f0bde0c, 
		0x7503911281ccbb18, 0x68ed1b4ca3e01ac4, 0x4ede85aec595f8a0, 0x53300ff0e7b9597c, 
		0xedcdea9b181b3288, 0xf02360c53a379354, 0xd610fe275c427130, 0xcbfe74797e6ed0ec, 
		0x9a77c3e390a9b5f8, 0x879949bdb2851424, 0xa1aad75fd4f0f640, 0xbc445d01f6dc579c, 
		0x74f1233d072c2a36, 0x691fa96325008bea, 0x4f2c37814375698e, 0x52c2bddf6159c852, 
		0x034b0a458f9ead46, 0x1ea5801badb20c9a, 0x38961ef9cbc7eefe, 0x257894a7e9eb4f22, 
		0x9b8571cc164924d6, 0x866bfb923465850a, 0xa05865705210676e, 0xbdb6ef2e703cc6b2, 
		0xec3f58b49efba3a6, 0xf1d1d2eabcd7027a, 0xd7e24c08daa2e01e, 0xca0cc656f88e41c2, 
		0x38c129f48ae82973, 0x252fa3aaa8c488af, 0x031c3d48ceb16acb, 0x1ef2b716ec9dcb17, 
		0x4f7b008c025aae03, 0x52958ad220760fdf, 0x74a614304603edbb, 0x69489e6e642f4c67, 
		0xd7b57b059b8d2793, 0xca5bf15bb9a1864f, 0xec686fb9dfd4642b, 0xf186e5e7fdf8c5f7, 
		0xa00f527d133fa0e3, 0xbde1d8233113013f, 0x9bd246c15766e35b, 0x863ccc9f754a4287, 
		0xec9136ae1ca42cbc, 0xf17fbcf03e888d60, 0xd74c221258fd6f04, 0xcaa2a84c7ad1ced8, 
		0x9b2b1fd69416abcc, 0x86c59588b63a0a10, 0xa0f60b6ad04fe874, 0xbd188134f26349a8, 
		0x03e5645f0dc1225c, 0x1e0bee012fed8380, 0x383870e3499861e4, 0x25d6fabd6bb4c038, 
		0x745f4d278573a52c, 0x69b1c779a75f04f0, 0x4f82599bc12ae694, 0x526cd3c5e3064748, 
		0xa0a13c6791602ff9, 0xbd4fb639b34c8e25, 0x9b7c28dbd5396c41, 0x8692a285f715cd9d, 
		0xd71b151f19d2a889, 0xcaf59f413bfe0955, 0xecc601a35d8beb31, 0xf1288bfd7fa74aed, 
		0x4fd56e9680052119, 0x523be4c8a22980c5, 0x74087a2ac45c62a1, 0x69e6f074e670c37d, 
		0x386f47ee08b7a669, 0x2581cdb02a9b07b5, 0x03b253524ceee5d1, 0x1e5cd90c6ec2440d,
	},
	{	
		0x0000000000000000, 0x5c2d776033c4205e, 0xb85aeec0678840bc, 0xe47799a0544c60e2, 
		0xe26d72ab601e9ffd, 0xbe4005cb53dabfa3, 0x5a379c6b0796df41, 0x061aeb0b3452ff1f, 
		0x56024a7d6f33217f, 0x0a2f3d1d5cf70121, 0xee58a4bd08bb61c3, 0xb275d3dd3b7f419d, 
		0xb46f38d60f2dbe82, 0xe8424fb63ce99edc, 0x0c35d61668a5fe3e, 0x5018a1765b61de60, 
		0xac0494fade6642fe, 0xf029e39aeda262a0, 0x145e7a3ab9ee0242, 0x48730d5a8a2a221c, 
		0x4e69e651be78dd03, 0x124491318dbcfd5d, 0xf6330891d9f09dbf, 0xaa1e7ff1ea34bde1, 
		0xfa06de87b1556381, 0xa62ba9e7829143df, 0x425c3047d6dd233d, 0x1e714727e5190363, 
		0x186bac2cd14bfc7c, 0x4446db4ce28fdc22, 0xa03142ecb6c3bcc0, 0xfc1c358c85079c9e, 
		0xcad186de13c29b79, 0x96fcf1be2006bb27, 0x728b681e744adbc5, 0x2ea61f7e478efb9b, 
		0x28bcf47573dc0484, 0x74918315401824da, 0x90e61ab514544438, 0xcccb6dd527906466, 
		0x9cd3cca37cf1ba06, 0xc0febbc34f359a58, 0x248922631b79faba, 0x78a4550328bddae4, 
		0x7ebebe081cef25fb, 0x2293c9682f2b05a5, 0xc6e450c87b676547, 0x9ac927a848a34519, 
		0x66d51224cda4d987, 0x3af86544fe60f9d9, 0xde8ffce4aa2c993b, 0x82a28b8499e8b965, 
		0x84b8608fadba467a, 0xd89517ef9e7e6624, 0x3ce28e4fca3206c6, 0x60cff92ff9f62698, 
		0x30d75859a297f8f8, 0x6cfa2f399153d8a6, 0x888db699c51fb844, 0xd4a0c1f9f6db981a, 
		0xd2ba2af2c2896705, 0x8e975d92f14d475b, 0x6ae0c432a50127b9, 0x36cdb35296c507e7, 
		0x077ba297888b2877, 0x5b56d5f7bb4f0829, 0xbf214c57ef0368cb, 0xe30c3b37dcc74895, 
		0xe516d03ce895b78a, 0xb93ba75cdb5197d4, 0x5d4c3efc8f1df736, 0x0161499cbcd9d768, 
		0x5179e8eae7b80908, 0x0d549f8ad47c2956, 0xe923062a803049b4, 0xb50e714ab3f469ea, 
		0xb3149a4187a696f5, 0xef39ed21b462b6ab, 0x0b4e7481e02ed649, 0x576303e1d3eaf617, 
		0xab7f366d56ed6a89, 0xf752410d65294ad7, 0x1325d8ad31652a35, 0x4f08afcd02a10a6b, 
		0x491244c636f3f574, 0x153f33a60537d52a, 0xf148aa06517bb5c8, 0xad65dd6662bf9596, 
		0xfd7d7c1039de4bf6, 0xa1500b700a1a6ba8, 0x452792d05e560b4a, 0x190ae5b06d922b14, 
		0x1f100ebb59c0d40b, 0x433d79db6a04f455, 0xa74ae07b3e4894b7, 0xfb67971b0d8cb4e9, 
		0xcdaa24499b49b30e, 0x91875329a88d9350, 0x75f0ca89fcc1f3b2, 0x29ddbde9cf05d3ec, 
		0x2fc756e2fb572cf3, 0x73ea2182c8930cad, 0x979db8229cdf6c4f, 0xcbb0cf42af1b4c11, 
		0x9ba86e34f47a9271, 0xc7851954c7beb22f, 0x23f280f493f2d2cd, 0x7fdff794a036f293, 
		0x79c51c9f94640d8c, 0x25e86bffa7a02dd2, 0xc19ff25ff3ec4d30, 0x9db2853fc0286d6e, 
		0x61aeb0b3452ff1f0, 0x3d83c7d376ebd1ae, 0xd9f45e7322a7b14c, 0x85d9291311639112, 
		0x83c3c21825316e0d, 0xdfeeb57816f54e53, 0x3b992cd842b92eb1, 0x67b45bb8717d0eef, 
		0x37acface2a1cd08f, 0x6b818dae19d8f0d1, 0x8ff6140e4d949033, 0xd3db636e7e50b06d, 
		0xd5c188654a024f72, 0x89ecff0579c66f2c, 0x6d9b66a52d8a0fce, 0x31b611c51e4e2f90, 
		0x0ef7452f111650ee, 0x52da324f22d270b0, 0xb6adabef769e1052, 0xea80dc8f455a300c, 
		0xec9a37847108cf13, 0xb0b740e442ccef4d, 0x54c0d94416808faf, 0x08edae242544aff1, 
		0x58f50f527e257191, 0x04d878324de151cf, 0xe0afe19219ad312d, 0xbc8296f22a691173, 
		0xba987df91e3bee6c, 0xe6b50a992dffce32, 0x02c2933979b3aed0, 0x5eefe4594a778e8e, 
		0xa2f3d1d5cf701210, 0xfedea6b5fcb4324e, 0x1aa93f15a8f852ac, 0x468448759b3c72f2, 
		0x409ea37eaf6e8ded, 0x1cb3d41e9caaadb3, 0xf8c44dbec8e6cd51, 0xa4e93adefb22ed0f, 
		0xf4f19ba8a043336f, 0xa8dcecc893871331, 0x4cab7568c7cb73d3, 0x10860208f40f538d, 
		0x169ce903c05dac92, 0x4ab19e63f3998ccc, 0xaec607c3a7d5ec2e, 0xf2eb70a39411cc70, 
		0xc426c3f102d4cb97, 0x980bb4913110ebc9, 0x7c7c2d31655c8b2b, 0x20515a515698ab75, 
		0x264bb15a62ca546a, 0x7a66c63a510e7434, 0x9e115f9a054214d6, 0xc23c28fa36863488, 
		0x9224898c6de7eae8, 0xce09feec5e23cab6, 0x2a7e674c0a6faa54, 0x7653102c39ab8a0a, 
		0x7049fb270df97515, 0x2c648c473e3d554b, 0xc81315e76a7135a9, 0x943e628759b515f7, 
		0x6822570bdcb28969, 0x340f206bef76a937, 0xd078b9cbbb3ac9d5, 0x8c55ceab88fee98b, 
		0x8a4f25a0bcac1694, 0xd66252c08f6836ca, 0x3215cb60db245628, 0x6e38bc00e8e07676, 
		0x3e201d76b381a816, 0x620d6a1680458848, 0x867af3b6d409e8aa, 0xda5784d6e7cdc8f4, 
		0xdc4d6fddd39f37eb, 0x806018bde05b17b5, 0x6417811db4177757, 0x383af67d87d35709, 
		0x098ce7b8999d7899, 0x55a190d8aa5958c7, 0xb1d60978fe153825, 0xedfb7e18cdd1187b, 
		0xebe19513f983e764, 0xb7cce273ca47c73a, 0x53bb7bd39e0ba7d8, 0x0f960cb3adcf8786, 
		0x5f8eadc5f6ae59e6, 0x03a3daa5c56a79b8, 0xe7d443059126195a, 0xbbf93465a2e23904, 
		0xbde3df6e96b0c61b, 0xe1cea80ea574e645, 0x05b931aef13886a7, 0x599446cec2fca6f9, 
		0xa588734247fb3a67, 0xf9a50422743f1a39, 0x1dd29d8220737adb, 0x41ffeae213b75a85, 
		0x47e501e927e5a59a, 0x1bc87689142185c4, 0xffbfef29406de526, 0xa392984973a9c578, 
		0xf38a393f28c81b18, 0xafa74e5f1b0c3b46, 0x4bd0d7ff4f405ba4, 0x17fda09f7c847bfa, 
		0x11e74b9448d684e5, 0x4dca3cf47b12a4bb, 0xa9bda5542f5ec459, 0xf590d2341c9ae407, 
		0xc35d61668a5fe3e0, 0x9f701606b99bc3be, 0x7b078fa6edd7a35c, 0x272af8c6de138302, 
		0x213013cdea417c1d, 0x7d1d64add9855c43, 0x996afd0d8dc93ca1, 0xc5478a6dbe0d1cff, 
		0x955f2b1be56cc29f, 0xc9725c7bd6a8e2c1, 0x2d05c5db82e48223, 0x7128b2bbb120a27d, 
		0x773259b085725d62, 0x2b1f2ed0b6b67d3c, 0xcf68b770e2fa1dde, 0x9345c010d13e3d80, 
		0x6f59f59c5439a11e, 0x337482fc67fd8140, 0xd7031b5c33b1e1a2, 0x8b2e6c3c0075c1fc, 
		0x8d34873734273ee3, 0xd119f05707e31ebd, 0x356e69f753af7e5f, 0x69431e97606b5e01, 
		0x395bbfe13b0a8061, 0x6576c88108cea03f, 0x810151215c82c0dd, 0xdd2c26416f46e083, 
		0xdb36cd4a5b141f9c, 0x871bba2a68d03fc2, 0x636c238a3c9c5f20, 0x3f4154ea0f587f7e,
	},
	{	
		0x0000000000000000, 0x6184d55f721267c6, 0xc309aabee424cf8c, 0xa28d7fe19636a84a, 
		0x14cbfa566747819d, 0x754f2f091555e65b, 0xd7c250e883634e11, 0xb64685b7f17129d7, 
		0x2997f4acce8f033a, 0x481321f3bc9d64fc, 0xea9e5e122aabccb6, 0x8b1a8b4d58b9ab70, 
		0x3d5c0efaa9c882a7, 0x5cd8dba5dbdae561, 0xfe55a4444dec4d2b, 0x9fd1711b3ffe2aed, 
		0x532fe9599d1e0674, 0x32ab3c06ef0c61b2, 0x902643e7793ac9f8, 0xf1a296b80b28ae3e, 
		0x47e4130ffa5987e9, 0x2660c650884be02f, 0x84edb9b11e7d4865, 0xe5696cee6c6f2fa3, 
		0x7ab81df55391054e, 0x1b3cc8aa21836288, 0xb9b1b74bb7b5cac2, 0xd8356214c5a7ad04, 
		0x6e73e7a334d684d3, 0x0ff732fc46c4e315, 0xad7a4d1dd0f24b5f, 0xccfe9842a2e02c99, 
		0xa65fd2b33a3c0ce8, 0xc7db07ec482e6b2e, 0x6556780dde18c364, 0x04d2ad52ac0aa4a2, 
		0xb29428e55d7b8d75, 0xd310fdba2f69eab3, 0x719d825bb95f42f9, 0x10195704cb4d253f, 
		0x8fc8261ff4b30fd2, 0xee4cf34086a16814, 0x4cc18ca11097c05e, 0x2d4559fe6285a798, 
		0x9b03dc4993f48e4f, 0xfa870916e1e6e989, 0x580a76f777d041c3, 0x398ea3a805c22605, 
		0xf5703beaa7220a9c, 0x94f4eeb5d5306d5a, 0x367991544306c510, 0x57fd440b3114a2d6, 
		0xe1bbc1bcc0658b01, 0x803f14e3b277ecc7, 0x22b26b022441448d, 0x4336be5d5653234b, 
		0xdce7cf4669ad09a6, 0xbd631a191bbf6e60, 0x1fee65f88d89c62a, 0x7e6ab0a7ff9ba1ec, 
		0xc82c35100eea883b, 0xa9a8e04f7cf8effd, 0x0b259faeeace47b7, 0x6aa14af198dc2071, 
		0xde670a4ddb760755, 0xbfe3df12a9646093, 0x1d6ea0f33f52c8d9, 0x7cea75ac4d40af1f, 
		0xcaacf01bbc3186c8, 0xab282544ce23e10e, 0x09a55aa558154944, 0x68218ffa2a072e82, 
		0xf7f0fee115f9046f, 0x96742bbe67eb63a9, 0x34f9545ff1ddcbe3, 0x557d810083cfac25, 
		0xe33b04b772be85f2, 0x82bfd1e800ace234, 0x2032ae09969a4a7e, 0x41b67b56e4882db8, 
		0x8d48e31446680121, 0xeccc364b347a66e7, 0x4e4149aaa24ccead, 0x2fc59cf5d05ea96b, 
		0x99831942212f80bc, 0xf807cc1d533de77a, 0x5a8ab3fcc50b4f30, 0x3b0e66a3b71928f6, 
		0xa4df17b888e7021b, 0xc55bc2e7faf565dd, 0x67d6bd066cc3cd97, 0x065268591ed1aa51, 
		0xb014edeeefa08386, 0xd19038b19db2e440, 0x731d47500b844c0a, 0x1299920f79962bcc, 
		0x7838d8fee14a0bbd, 0x19bc0da193586c7b, 0xbb317240056ec431, 0xdab5a71f777ca3f7, 
		0x6cf322a8860d8a20, 0x0d77f7f7f41fede6, 0xaffa8816622945ac, 0xce7e5d49103b226a, 
		0x51af2c522fc50887, 0x302bf90d5dd76f41, 0x92a686eccbe1c70b, 0xf32253b3b9f3a0cd, 
		0x4564d6044882891a, 0x24e0035b3a90eedc, 0x866d7cbaaca64696, 0xe7e9a9e5deb42150, 
		0x2b1731a77c540dc9, 0x4a93e4f80e466a0f, 0xe81e9b199870c245, 0x899a4e46ea62a583, 
		0x3fdccbf11b138c54, 0x5e581eae6901eb92, 0xfcd5614fff3743d8, 0x9d51b4108d25241e, 
		0x0280c50bb2db0ef3, 0x63041054c0c96935, 0xc1896fb556ffc17f, 0xa00dbaea24eda6b9, 
		0x164b3f5dd59c8f6e, 0x77cfea02a78ee8a8, 0xd54295e331b840e2, 0xb4c640bc43aa2724, 
		0x2e16bbb019e2102f, 0x4f926eef6bf077e9, 0xed1f110efdc6dfa3, 0x8c9bc4518fd4b865, 
		0x3add41e67ea591b2, 0x5b5994b90cb7f674, 0xf9d4eb589a815e3e, 0x98503e07e89339f8, 
		0x07814f1cd76d1315, 0x66059a43a57f74d3, 0xc488e5a23349dc99, 0xa50c30fd415bbb5f, 
		0x134ab54ab02a9288, 0x72ce6015c238f54e, 0xd0431ff4540e5d04, 0xb1c7caab261c3ac2, 
		0x7d3952e984fc165b, 0x1cbd87b6f6ee719d, 0xbe30f85760d8d9d7, 0xdfb42d0812cabe11, 
		0x69f2a8bfe3bb97c6, 0x08767de091a9f000, 0xaafb0201079f584a, 0xcb7fd75e758d3f8c, 
		0x54aea6454a731561, 0x352a731a386172a7, 0x97a70cfbae57daed, 0xf623d9a4dc45bd2b, 
		0x40655c132d3494fc, 0x21e1894c5f26f33a, 0x836cf6adc9105b70, 0xe2e823f2bb023cb6, 
		0x8849690323de1cc7, 0xe9cdbc5c51cc7b01, 0x4b40c3bdc7fad34b, 0x2ac416e2b5e8b48d, 
		0x9c82935544999d5a, 0xfd06460a368bfa9c, 0x5f8b39eba0bd52d6, 0x3e0fecb4d2af3510, 
		0xa1de9dafed511ffd, 0xc05a48f09f43783b, 0x62d737110975d071, 0x0353e24e7b67b7b7, 
		0xb51567f98a169e60, 0xd491b2a6f804f9a6, 0x761ccd476e3251ec, 0x179818181c20362a, 
		0xdb66805abec01ab3, 0xbae25505ccd27d75, 0x186f2ae45ae4d53f, 0x79ebffbb28f6b2f9, 
		0xcfad7a0cd9879b2e, 0xae29af53ab95fce8, 0x0ca4d0b23da354a2, 0x6d2005ed4fb13364, 
		0xf2f174f6704f1989, 0x9375a1a9025d7e4f, 0x31f8de48946bd605, 0x507c0b17e679b1c3, 
		0xe63a8ea017089814, 0x87be5bff651affd2, 0x2533241ef32c5798, 0x44b7f141813e305e, 
		0xf071b1fdc294177a, 0x91f564a2b08670bc, 0x33781b4326b0d8f6, 0x52fcce1c54a2bf30, 
		0xe4ba4baba5d396e7, 0x853e9ef4d7c1f121, 0x27b3e11541f7596b, 0x4637344a33e53ead, 
		0xd9e645510c1b1440, 0xb862900e7e097386, 0x1aefefefe83fdbcc, 0x7b6b3ab09a2dbc0a, 
		0xcd2dbf076b5c95dd, 0xaca96a58194ef21b, 0x0e2415b98f785a51, 0x6fa0c0e6fd6a3d97, 
		0xa35e58a45f8a110e, 0xc2da8dfb2d9876c8, 0x6057f21abbaede82, 0x01d32745c9bcb944, 
		0xb795a2f238cd9093, 0xd61177ad4adff755, 0x749c084cdce95f1f, 0x1518dd13aefb38d9, 
		0x8ac9ac0891051234, 0xeb4d7957e31775f2, 0x49c006b67521ddb8, 0x2844d3e90733ba7e, 
		0x9e02565ef64293a9, 0xff8683018450f46f, 0x5d0bfce012665c25, 0x3c8f29bf60743be3, 
		0x562e634ef8a81b92, 0x37aab6118aba7c54, 0x9527c9f01c8cd41e, 0xf4a31caf6e9eb3d8, 
		0x42e599189fef9a0f, 0x23614c47edfdfdc9, 0x81ec33a67bcb5583, 0xe068e6f909d93245, 
		0x7fb997e2362718a8, 0x1e3d42bd44357f6e, 0xbcb03d5cd203d724, 0xdd34e803a011b0e2, 
		0x6b726db451609935, 0x0af6b8eb2372fef3, 0xa87bc70ab54456b9, 0xc9ff1255c756317f, 
		0x05018a1765b61de6, 0x64855f4817a47a20, 0xc60820a98192d26a, 0xa78cf5f6f380b5ac, 
		0x11ca704102f19c7b, 0x704ea51e70e3fbbd, 0xd2c3daffe6d553f7, 0xb3470fa094c73431, 
		0x2c967ebbab391edc, 0x4d12abe4d92b791a, 0xef9fd4054f1dd150, 0x8e1b015a3d0fb696, 
		0x385d84edcc7e9f41, 0x59d951b2be6cf887, 0xfb542e53285a50cd, 0x9ad0fb0c5a48370b,
	},
	{	
		0x0000000000000000, 0x22ef0d5934f964ec, 0x45de1ab269f2c9d8, 0x673117eb5d0bad34, 
		0x8bbc3564d3e593b0, 0xa953383de71cf75c, 0xce622fd6ba175a68, 0xec8d228f8eee3e84, 
		0x85a0c5e208c539e5, 0xa74fc8bb3c3c5d09, 0xc07edf506137f03d, 0xe291d20955ce94d1, 
		0x0e1cf086db20aa55, 0x2cf3fddfefd9ceb9, 0x4bc2ea34b2d2638d, 0x692de76d862b0761, 
		0x999924efbe846d4f, 0xbb7629b68a7d09a3, 0xdc473e5dd776a497, 0xfea83304e38fc07b, 
		0x1225118b6d61feff, 0x30ca1cd259989a13, 0x57fb0b3904933727, 0x75140660306a53cb, 
		0x1c39e10db64154aa, 0x3ed6ec5482b83046, 0x59e7fbbfdfb39d72, 0x7b08f6e6eb4af99e, 
		0x9785d46965a4c71a, 0xb56ad930515da3f6, 0xd25bcedb0c560ec2, 0xf0b4c38238af6a2e, 
		0xa1eae6f4d206c41b, 0x8305ebade6ffa0f7, 0xe434fc46bbf40dc3, 0xc6dbf11f8f0d692f, 
		0x2a56d39001e357ab, 0x08b9dec9351a3347, 0x6f88c92268119e73, 0x4d67c47b5ce8fa9f, 
		0x244a2316dac3fdfe, 0x06a52e4fee3a9912, 0x619439a4b3313426, 0x437b34fd87c850ca, 
		0xaff6167209266e4e, 0x8d191b2b3ddf0aa2, 0xea280cc060d4a796, 0xc8c70199542dc37a, 
		0x3873c21b6c82a954, 0x1a9ccf42587bcdb8, 0x7dadd8a90570608c, 0x5f42d5f031890460, 
		0xb3cff77fbf673ae4, 0x9120fa268b9e5e08, 0xf611edcdd695f33c, 0xd4fee094e26c97d0, 
		0xbdd307f9644790b1, 0x9f3c0aa050bef45d, 0xf80d1d4b0db55969, 0xdae21012394c3d85, 
		0x366f329db7a20301, 0x14803fc4835b67ed, 0x73b1282fde50cad9, 0x515e2576eaa9ae35, 
		0xd10d62c20b0396b3, 0xf3e26f9b3ffaf25f, 0x94d3787062f15f6b, 0xb63c752956083b87, 
		0x5ab157a6d8e60503, 0x785e5affec1f61ef, 0x1f6f4d14b114ccdb, 0x3d80404d85eda837, 
		0x54ada72003c6af56, 0x7642aa79373fcbba, 0x1173bd926a34668e, 0x339cb0cb5ecd0262, 
		0xdf119244d0233ce6, 0xfdfe9f1de4da580a, 0x9acf88f6b9d1f53e, 0xb82085af8d2891d2, 
		0x4894462db587fbfc, 0x6a7b4b74817e9f10, 0x0d4a5c9fdc753224, 0x2fa551c6e88c56c8, 
		0xc32873496662684c, 0xe1c77e10529b0ca0, 0x86f669fb0f90a194, 0xa41964a23b69c578, 
		0xcd3483cfbd42c219, 0xefdb8e9689bba6f5, 0x88ea997dd4b00bc1, 0xaa059424e0496f2d, 
		0x4688b6ab6ea751a9, 0x6467bbf25a5e3545, 0x0356ac1907559871, 0x21b9a14033acfc9d, 
		0x70e78436d90552a8, 0x5208896fedfc3644, 0x35399e84b0f79b70, 0x17d693dd840eff9c, 
		0xfb5bb1520ae0c118, 0xd9b4bc0b3e19a5f4, 0xbe85abe0631208c0, 0x9c6aa6b957eb6c2c, 
		0xf54741d4d1c06b4d, 0xd7a84c8de5390fa1, 0xb0995b66b832a295, 0x9276563f8ccbc679, 
		0x7efb74b00225f8fd, 0x5c1479e936dc9c11, 0x3b256e026bd73125, 0x19ca635b5f2e55c9, 
		0xe97ea0d967813fe7, 0xcb91ad8053785b0b, 0xaca0ba6b0e73f63f, 0x8e4fb7323a8a92d3, 
		0x62c295bdb464ac57, 0x402d98e4809dc8bb, 0x271c8f0fdd96658f, 0x05f38256e96f0163, 
		0x6cde653b6f440602, 0x4e3168625bbd62ee, 0x29007f8906b6cfda, 0x0bef72d0324fab36, 
		0xe762505fbca195b2, 0xc58d5d068858f15e, 0xa2bc4aedd5535c6a, 0x805347b4e1aa3886, 
		0x30c26aafb90933e3, 0x122d67f68df0570f, 0x751c701dd0fbfa3b, 0x57f37d44e4029ed7, 
		0xbb7e5fcb6aeca053, 0x999152925e15c4bf, 0xfea04579031e698b, 0xdc4f482037e70d67, 
		0xb562af4db1cc0a06, 0x978da21485356eea, 0xf0bcb5ffd83ec3de, 0xd253b8a6ecc7a732, 
		0x3ede9a29622999b6, 0x1c31977056d0fd5a, 0x7b00809b0bdb506e, 0x59ef8dc23f223482, 
		0xa95b4e40078d5eac, 0x8bb4431933743a40, 0xec8554f26e7f9774, 0xce6a59ab5a86f398, 
		0x22e77b24d468cd1c, 0x0008767de091a9f0, 0x67396196bd9a04c4, 0x45d66ccf89636028, 
		0x2cfb8ba20f486749, 0x0e1486fb3bb103a5, 0x6925911066baae91, 0x4bca9c495243ca7d, 
		0xa747bec6dcadf4f9, 0x85a8b39fe8549015, 0xe299a474b55f3d21, 0xc076a92d81a659cd, 
		0x91288c5b6b0ff7f8, 0xb3c781025ff69314, 0xd4f696e902fd3e20, 0xf6199bb036045acc, 
		0x1a94b93fb8ea6448, 0x387bb4668c1300a4, 0x5f4aa38dd118ad90, 0x7da5aed4e5e1c97c, 
		0x148849b963cace1d, 0x366744e05733aaf1, 0x5156530b0a3807c5, 0x73b95e523ec16329, 
		0x9f347cddb02f5dad, 0xbddb718484d63941, 0xdaea666fd9dd9475, 0xf8056b36ed24f099, 
		0x08b1a8b4d58b9ab7, 0x2a5ea5ede172fe5b, 0x4d6fb206bc79536f, 0x6f80bf5f88803783, 
		0x830d9dd0066e0907, 0xa1e2908932976deb, 0xc6d387626f9cc0df, 0xe43c8a3b5b65a433, 
		0x8d116d56dd4ea352, 0xaffe600fe9b7c7be, 0xc8cf77e4b4bc6a8a, 0xea207abd80450e66, 
		0x06ad58320eab30e2, 0x2442556b3a52540e, 0x437342806759f93a, 0x619c4fd953a09dd6, 
		0xe1cf086db20aa550, 0xc320053486f3c1bc, 0xa41112dfdbf86c88, 0x86fe1f86ef010864, 
		0x6a733d0961ef36e0, 0x489c30505516520c, 0x2fad27bb081dff38, 0x0d422ae23ce49bd4, 
		0x646fcd8fbacf9cb5, 0x4680c0d68e36f859, 0x21b1d73dd33d556d, 0x035eda64e7c43181, 
		0xefd3f8eb692a0f05, 0xcd3cf5b25dd36be9, 0xaa0de25900d8c6dd, 0x88e2ef003421a231, 
		0x78562c820c8ec81f, 0x5ab921db3877acf3, 0x3d883630657c01c7, 0x1f673b695185652b, 
		0xf3ea19e6df6b5baf, 0xd10514bfeb923f43, 0xb6340354b6999277, 0x94db0e0d8260f69b, 
		0xfdf6e960044bf1fa, 0xdf19e43930b29516, 0xb828f3d26db93822, 0x9ac7fe8b59405cce, 
		0x764adc04d7ae624a, 0x54a5d15de35706a6, 0x3394c6b6be5cab92, 0x117bcbef8aa5cf7e, 
		0x4025ee99600c614b, 0x62cae3c054f505a7, 0x05fbf42b09fea893, 0x2714f9723d07cc7f, 
		0xcb99dbfdb3e9f2fb, 0xe976d6a487109617, 0x8e47c14fda1b3b23, 0xaca8cc16eee25fcf, 
		0xc5852b7b68c958ae, 0xe76a26225c303c42, 0x805b31c9013b9176, 0xa2b43c9035c2f59a, 
		0x4e391e1fbb2ccb1e, 0x6cd613468fd5aff2, 0x0be704add2de02c6, 0x290809f4e627662a, 
		0xd9bcca76de880c04, 0xfb53c72fea7168e8, 0x9c62d0c4b77ac5dc, 0xbe8ddd9d8383a130, 
		0x5200ff120d6d9fb4, 0x70eff24b3994fb58, 0x17dee5a0649f566c, 0x3531e8f950663280, 
		0x5c1c0f94d64d35e1, 0x7ef302cde2b4510d, 0x19c21526bfbffc39, 0x3b2d187f8b4698d5, 
		0xd7a03af005a8a651, 0xf54f37a93151c2bd, 0x927e20426c5a6f89, 0xb0912d1b58a30b65,
	},
	{	
		0x0000000000000000, 0xdabe95afc7875f40, 0x27a584742000a005, 0xfd1b11dbe787ff45, 
		0x4f4b08e84001400a, 0x95f59d4787861f4a, 0x68ee8c9c6001e00f, 0xb2501933a786bf4f, 
		0x9e9611d080028014, 0x4428847f4785df54, 0xb93395a4a0022011, 0x638d000b67857f51, 
		0xd1dd1938c003c01e, 0x0b638c9707849f5e, 0xf6789d4ce003601b, 0x2cc608e327843f5b, 
		0xaff48c8aaf0b1ead, 0x754a1925688c41ed, 0x885108fe8f0bbea8, 0x52ef9d51488ce1e8, 
		0xe0bf8462ef0a5ea7, 0x3a0111cd288d01e7, 0xc71a0016cf0afea2, 0x1da495b9088da1e2, 
		0x31629d5a2f099eb9, 0xebdc08f5e88ec1f9, 0x16c7192e0f093ebc, 0xcc798c81c88e61fc, 
		0x7e2995b26f08deb3, 0xa497001da88f81f3, 0x598c11c64f087eb6, 0x83328469888f21f6, 
		0xcd31b63ef11823df, 0x178f2391369f7c9f, 0xea94324ad11883da, 0x302aa7e5169fdc9a, 
		0x827abed6b11963d5, 0x58c42b79769e3c95, 0xa5df3aa29119c3d0, 0x7f61af0d569e9c90, 
		0x53a7a7ee711aa3cb, 0x89193241b69dfc8b, 0x7402239a511a03ce, 0xaebcb635969d5c8e, 
		0x1cecaf06311be3c1, 0xc6523aa9f69cbc81, 0x3b492b72111b43c4, 0xe1f7beddd69c1c84, 
		0x62c53ab45e133d72, 0xb87baf1b99946232, 0x4560bec07e139d77, 0x9fde2b6fb994c237, 
		0x2d8e325c1e127d78, 0xf730a7f3d9952238, 0x0a2bb6283e12dd7d, 0xd0952387f995823d, 
		0xfc532b64de11bd66, 0x26edbecb1996e226, 0xdbf6af10fe111d63, 0x01483abf39964223, 
		0xb318238c9e10fd6c, 0x69a6b6235997a22c, 0x94bda7f8be105d69, 0x4e03325779970229, 
		0x08bbc3564d3e593b, 0xd20556f98ab9067b, 0x2f1e47226d3ef93e, 0xf5a0d28daab9a67e, 
		0x47f0cbbe0d3f1931, 0x9d4e5e11cab84671, 0x60554fca2d3fb934, 0xbaebda65eab8e674, 
		0x962dd286cd3cd92f, 0x4c9347290abb866f, 0xb18856f2ed3c792a, 0x6b36c35d2abb266a, 
		0xd966da6e8d3d9925, 0x03d84fc14abac665, 0xfec35e1aad3d3920, 0x247dcbb56aba6660, 
		0xa74f4fdce2354796, 0x7df1da7325b218d6, 0x80eacba8c235e793, 0x5a545e0705b2b8d3, 
		0xe8044734a234079c, 0x32bad29b65b358dc, 0xcfa1c3408234a799, 0x151f56ef45b3f8d9, 
		0x39d95e0c6237c782, 0xe367cba3a5b098c2, 0x1e7cda7842376787, 0xc4c24fd785b038c7, 
		0x769256e422368788, 0xac2cc34be5b1d8c8, 0x5137d2900236278d, 0x8b89473fc5b178cd, 
		0xc58a7568bc267ae4, 0x1f34e0c77ba125a4, 0xe22ff11c9c26dae1, 0x389164b35ba185a1, 
		0x8ac17d80fc273aee, 0x507fe82f3ba065ae, 0xad64f9f4dc279aeb, 0x77da6c5b1ba0c5ab, 
		0x5b1c64b83c24faf0, 0x81a2f117fba3a5b0, 0x7cb9e0cc1c245af5, 0xa6077563dba305b5, 
		0x14576c507c25bafa, 0xcee9f9ffbba2e5ba, 0x33f2e8245c251aff, 0xe94c7d8b9ba245bf, 
		0x6a7ef9e2132d6449, 0xb0c06c4dd4aa3b09, 0x4ddb7d96332dc44c, 0x9765e839f4aa9b0c, 
		0x2535f10a532c2443, 0xff8b64a594ab7b03, 0x0290757e732c8446, 0xd82ee0d1b4abdb06, 
		0xf4e8e832932fe45d, 0x2e567d9d54a8bb1d, 0xd34d6c46b32f4458, 0x09f3f9e974a81b18, 
		0xbba3e0dad32ea457, 0x611d757514a9fb17, 0x9c0664aef32e0452, 0x46b8f10134a95b12, 
		0x117786ac9a7cb276, 0xcbc913035dfbed36, 0x36d202d8ba7c1273, 0xec6c97777dfb4d33, 
		0x5e3c8e44da7df27c, 0x84821beb1dfaad3c, 0x79990a30fa7d5279, 0xa3279f9f3dfa0d39, 
		0x8fe1977c1a7e3262, 0x555f02d3ddf96d22, 0xa84413083a7e9267, 0x72fa86a7fdf9cd27, 
		0xc0aa9f945a7f7268, 0x1a140a3b9df82d28, 0xe70f1be07a7fd26d, 0x3db18e4fbdf88d2d, 
		0xbe830a263577acdb, 0x643d9f89f2f0f39b, 0x99268e5215770cde, 0x43981bfdd2f0539e, 
		0xf1c802ce7576ecd1, 0x2b769761b2f1b391, 0xd66d86ba55764cd4, 0x0cd3131592f11394, 
		0x20151bf6b5752ccf, 0xfaab8e5972f2738f, 0x07b09f8295758cca, 0xdd0e0a2d52f2d38a, 
		0x6f5e131ef5746cc5, 0xb5e086b132f33385, 0x48fb976ad574ccc0, 0x924502c512f39380, 
		0xdc4630926b6491a9, 0x06f8a53dace3cee9, 0xfbe3b4e64b6431ac, 0x215d21498ce36eec, 
		0x930d387a2b65d1a3, 0x49b3add5ece28ee3, 0xb4a8bc0e0b6571a6, 0x6e1629a1cce22ee6, 
		0x42d02142eb6611bd, 0x986eb4ed2ce14efd, 0x6575a536cb66b1b8, 0xbfcb30990ce1eef8, 
		0x0d9b29aaab6751b7, 0xd725bc056ce00ef7, 0x2a3eadde8b67f1b2, 0xf08038714ce0aef2, 
		0x73b2bc18c46f8f04, 0xa90c29b703e8d044, 0x5417386ce46f2f01, 0x8ea9adc323e87041, 
		0x3cf9b4f0846ecf0e, 0xe647215f43e9904e, 0x1b5c3084a46e6f0b, 0xc1e2a52b63e9304b, 
		0xed24adc8446d0f10, 0x379a386783ea5050, 0xca8129bc646daf15, 0x103fbc13a3eaf055, 
		0xa26fa520046c4f1a, 0x78d1308fc3eb105a, 0x85ca2154246cef1f, 0x5f74b4fbe3ebb05f, 
		0x19cc45fad742eb4d, 0xc372d05510c5b40d, 0x3e69c18ef7424b48, 0xe4d7542130c51408, 
		0x56874d129743ab47, 0x8c39d8bd50c4f407, 0x7122c966b7430b42, 0xab9c5cc970c45402, 
		0x875a542a57406b59, 0x5de4c18590c73419, 0xa0ffd05e7740cb5c, 0x7a4145f1b0c7941c, 
		0xc8115cc217412b53, 0x12afc96dd0c67413, 0xefb4d8b637418b56, 0x350a4d19f0c6d416, 
		0xb638c9707849f5e0, 0x6c865cdfbfceaaa0, 0x919d4d04584955e5, 0x4b23d8ab9fce0aa5, 
		0xf973c1983848b5ea, 0x23cd5437ffcfeaaa, 0xded645ec184815ef, 0x0468d043dfcf4aaf, 
		0x28aed8a0f84b75f4, 0xf2104d0f3fcc2ab4, 0x0f0b5cd4d84bd5f1, 0xd5b5c97b1fcc8ab1, 
		0x67e5d048b84a35fe, 0xbd5b45e77fcd6abe, 0x4040543c984a95fb, 0x9afec1935fcdcabb, 
		0xd4fdf3c4265ac892, 0x0e43666be1dd97d2, 0xf35877b0065a6897, 0x29e6e21fc1dd37d7, 
		0x9bb6fb2c665b8898, 0x41086e83a1dcd7d8, 0xbc137f58465b289d, 0x66adeaf781dc77dd, 
		0x4a6be214a6584886, 0x90d577bb61df17c6, 0x6dce66608658e883, 0xb770f3cf41dfb7c3, 
		0x0520eafce659088c, 0xdf9e7f5321de57cc, 0x22856e88c659a889, 0xf83bfb2701def7c9, 
		0x7b097f4e8951d63f, 0xa1b7eae14ed6897f, 0x5cacfb3aa951763a, 0x86126e956ed6297a, 
		0x344277a6c9509635, 0xeefce2090ed7c975, 0x13e7f3d2e9503630, 0xc959667d2ed76970, 
		0xe59f6e9e0953562b, 0x3f21fb31ced4096b, 0xc23aeaea2953f62e, 0x18847f45eed4a96e, 
		0xaad4667649521621, 0x706af3d98ed54961, 0x8d71e2026952b624, 0x57cf77adaed5e964,
	},
}

@private _crc64_table_iso_3306 := [256]u16{
	0x0000, 0x01b0, 0x0360, 0x02d0, 
	0x06c0, 0x0770, 0x05a0, 0x0410, 
	0x0d80, 0x0c30, 0x0ee0, 0x0f50, 
	0x0b40, 0x0af0, 0x0820, 0x0990, 
	0x1b00, 0x1ab0, 0x1860, 0x19d0, 
	0x1dc0, 0x1c70, 0x1ea0, 0x1f10, 
	0x1680, 0x1730, 0x15e0, 0x1450, 
	0x1040, 0x11f0, 0x1320, 0x1290, 
	0x3600, 0x37b0, 0x3560, 0x34d0, 
	0x30c0, 0x3170, 0x33a0, 0x3210, 
	0x3b80, 0x3a30, 0x38e0, 0x3950, 
	0x3d40, 0x3cf0, 0x3e20, 0x3f90, 
	0x2d00, 0x2cb0, 0x2e60, 0x2fd0, 
	0x2bc0, 0x2a70, 0x28a0, 0x2910, 
	0x2080, 0x2130, 0x23e0, 0x2250, 
	0x2640, 0x27f0, 0x2520, 0x2490, 
	0x6c00, 0x6db0, 0x6f60, 0x6ed0, 
	0x6ac0, 0x6b70, 0x69a0, 0x6810, 
	0x6180, 0x6030, 0x62e0, 0x6350, 
	0x6740, 0x66f0, 0x6420, 0x6590, 
	0x7700, 0x76b0, 0x7460, 0x75d0, 
	0x71c0, 0x7070, 0x72a0, 0x7310, 
	0x7a80, 0x7b30, 0x79e0, 0x7850, 
	0x7c40, 0x7df0, 0x7f20, 0x7e90, 
	0x5a00, 0x5bb0, 0x5960, 0x58d0, 
	0x5cc0, 0x5d70, 0x5fa0, 0x5e10, 
	0x5780, 0x5630, 0x54e0, 0x5550, 
	0x5140, 0x50f0, 0x5220, 0x5390, 
	0x4100, 0x40b0, 0x4260, 0x43d0, 
	0x47c0, 0x4670, 0x44a0, 0x4510, 
	0x4c80, 0x4d30, 0x4fe0, 0x4e50, 
	0x4a40, 0x4bf0, 0x4920, 0x4890, 
	0xd800, 0xd9b0, 0xdb60, 0xdad0, 
	0xdec0, 0xdf70, 0xdda0, 0xdc10, 
	0xd580, 0xd430, 0xd6e0, 0xd750, 
	0xd340, 0xd2f0, 0xd020, 0xd190, 
	0xc300, 0xc2b0, 0xc060, 0xc1d0, 
	0xc5c0, 0xc470, 0xc6a0, 0xc710, 
	0xce80, 0xcf30, 0xcde0, 0xcc50, 
	0xc840, 0xc9f0, 0xcb20, 0xca90, 
	0xee00, 0xefb0, 0xed60, 0xecd0, 
	0xe8c0, 0xe970, 0xeba0, 0xea10, 
	0xe380, 0xe230, 0xe0e0, 0xe150, 
	0xe540, 0xe4f0, 0xe620, 0xe790, 
	0xf500, 0xf4b0, 0xf660, 0xf7d0, 
	0xf3c0, 0xf270, 0xf0a0, 0xf110, 
	0xf880, 0xf930, 0xfbe0, 0xfa50, 
	0xfe40, 0xfff0, 0xfd20, 0xfc90, 
	0xb400, 0xb5b0, 0xb760, 0xb6d0, 
	0xb2c0, 0xb370, 0xb1a0, 0xb010, 
	0xb980, 0xb830, 0xbae0, 0xbb50, 
	0xbf40, 0xbef0, 0xbc20, 0xbd90, 
	0xaf00, 0xaeb0, 0xac60, 0xadd0, 
	0xa9c0, 0xa870, 0xaaa0, 0xab10, 
	0xa280, 0xa330, 0xa1e0, 0xa050, 
	0xa440, 0xa5f0, 0xa720, 0xa690, 
	0x8200, 0x83b0, 0x8160, 0x80d0, 
	0x84c0, 0x8570, 0x87a0, 0x8610, 
	0x8f80, 0x8e30, 0x8ce0, 0x8d50, 
	0x8940, 0x88f0, 0x8a20, 0x8b90, 
	0x9900, 0x98b0, 0x9a60, 0x9bd0, 
	0x9fc0, 0x9e70, 0x9ca0, 0x9d10, 
	0x9480, 0x9530, 0x97e0, 0x9650, 
	0x9240, 0x93f0, 0x9120, 0x9090,
}
