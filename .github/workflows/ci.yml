name: CI
on: [push, pull_request, workflow_dispatch]

jobs:
  build_netbsd:
    name: NetBSD Build, Check, and Test
    runs-on: ubuntu-latest
    env:
      PKGSRC_BRANCH: 2024Q3
    steps:
    - uses: actions/checkout@v4
    - name: Build, Check, and Test
      timeout-minutes: 15
      uses: vmactions/netbsd-vm@v1
      with:
        release: "10.0"
        envs: PKGSRC_BRANCH
        usesh: true
        copyback: false
        prepare: |
          PKG_PATH="https://cdn.NetBSD.org/pub/pkgsrc/packages/NetBSD/amd64/$(uname -r | cut -d_ -f1)_${PKGSRC_BRANCH}/All" /usr/sbin/pkg_add pkgin
          pkgin -y in gmake git bash python311 llvm clang
          ln -s /usr/pkg/bin/python3.11 /usr/bin/python3
        run: |
          set -e -x
          git config --global --add safe.directory $(pwd)
          gmake release
          ./odin version
          ./odin report
          gmake -C vendor/stb/src
          gmake -C vendor/cgltf/src
          gmake -C vendor/miniaudio/src
          ./odin check examples/all -vet -strict-style -disallow-do -target:netbsd_amd64
          ./odin check examples/all -vet -strict-style -disallow-do -target:netbsd_arm64
          ./odin check examples/all/sdl3 -vet -strict-style -disallow-do -target:netbsd_amd64 -no-entry-point
          ./odin check examples/all/sdl3 -vet -strict-style -disallow-do -target:netbsd_arm64 -no-entry-point
          ./odin test tests/core/normal.odin -file -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true
          ./odin test tests/core/speed.odin -file -all-packages -vet -strict-style -disallow-do -o:speed -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true
          ./odin test tests/vendor -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true
          (cd tests/issues; ./run.sh)
          ./odin check tests/benchmark -vet -strict-style -no-entry-point

  build_freebsd:
    name: FreeBSD Build, Check, and Test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Build, Check, and Test
      timeout-minutes: 15
      uses: vmactions/freebsd-vm@v1
      with:
        usesh: true
        copyback: false
        prepare: |
          pkg install -y gmake git bash python3 libxml2 llvm18
        run: |
          # `set -e` is needed for test failures to register. https://github.com/vmactions/freebsd-vm/issues/72
          set -e -x
          git config --global --add safe.directory $(pwd)
          gmake release
          ./odin version
          ./odin report
          gmake -C vendor/stb/src
          gmake -C vendor/cgltf/src
          gmake -C vendor/miniaudio/src
          ./odin check examples/all -vet -strict-style -disallow-do -target:freebsd_amd64
          ./odin check examples/all/sdl3 -vet -strict-style -disallow-do -target:freebsd_amd64 -no-entry-point
          ./odin test tests/core/normal.odin -file -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true
          ./odin test tests/core/speed.odin -file -all-packages -vet -strict-style -disallow-do -o:speed -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true
          ./odin test tests/vendor -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true
          (cd tests/issues; ./run.sh)
          ./odin check tests/benchmark -vet -strict-style -no-entry-point
  ci:
    strategy:
      fail-fast: false
      matrix:
        # MacOS 13 runs on Intel, 14 runs on ARM
        os: [macos-13, macos-14, ubuntu-latest]
    runs-on: ${{ matrix.os }}
    name: ${{ matrix.os == 'macos-14' && 'MacOS ARM' || (matrix.os == 'macos-13' && 'MacOS Intel') || (matrix.os == 'ubuntu-latest' && 'Ubuntu') }} Build, Check, and Test
    timeout-minutes: 15
    steps:

      - uses: actions/checkout@v4

      - name: Download LLVM (MacOS Intel)
        if: matrix.os == 'macos-13'
        run: |
          brew update
          brew install llvm@20 lua@5.4 lld
          echo "$(brew --prefix llvm@20)/bin" >> $GITHUB_PATH

      - name: Download LLVM (MacOS ARM)
        if: matrix.os == 'macos-14'
        run: |
          brew update
          brew install llvm@20 wasmtime lua@5.4 lld
          echo "$(brew --prefix llvm@20)/bin" >> $GITHUB_PATH

      - name: Download LLVM (Ubuntu)
        if: matrix.os == 'ubuntu-latest'
        run: |
          wget https://apt.llvm.org/llvm.sh
          chmod +x llvm.sh
          sudo ./llvm.sh 20
          echo "/usr/lib/llvm-20/bin" >> $GITHUB_PATH

      - name: Build Odin
        run: ./build_odin.sh release
      - name: Odin version
        run: ./odin version
      - name: Odin report
        run: ./odin report
      - name: Compile needed Vendor
        run: |
          make -C vendor/stb/src
          make -C vendor/cgltf/src
          make -C vendor/miniaudio/src
      - name: Odin check
        run: ./odin check examples/demo -vet
      - name: Odin run
        run: ./odin run examples/demo
      - name: Odin run -debug
        run: ./odin run examples/demo -debug
      - name: Odin check examples/all
        run: ./odin check examples/all -strict-style -vet -disallow-do
      - name: Odin check examples/all/sdl3
        run: ./odin check examples/all/sdl3  -strict-style -vet -disallow-do -no-entry-point
      - name: Normal Core library tests
        run: ./odin test tests/core/normal.odin -file -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -sanitize:address
      - name: Optimized Core library tests
        run: ./odin test tests/core/speed.odin -o:speed -file -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -sanitize:address
      - name: Vendor library tests
        run: ./odin test tests/vendor -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -sanitize:address
      - name: Internals tests
        run: ./odin test tests/internal -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -sanitize:address
      - name: GitHub Issue tests
        run: |
          cd tests/issues
          ./run.sh

      - name: Run demo on WASI WASM32
        run: |
          ./odin build examples/demo -target:wasi_wasm32 -vet -strict-style -disallow-do -out:demo
          wasmtime ./demo.wasm
        if: matrix.os == 'macos-14'

      - name: Check benchmarks
        run: ./odin check tests/benchmark -vet -strict-style -no-entry-point
      - name: Odin check examples/all for Linux i386
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all -vet -strict-style -disallow-do -target:linux_i386
      - name: Odin check examples/all for Linux arm64
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all -vet -strict-style -disallow-do -target:linux_arm64
      - name: Odin check examples/all for FreeBSD amd64
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all -vet -strict-style -disallow-do -target:freebsd_amd64
      - name: Odin check examples/all for OpenBSD amd64
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all -vet -strict-style -disallow-do -target:openbsd_amd64
      - name: Odin check examples/all for js_wasm32
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all -vet -strict-style -disallow-do -no-entry-point -target:js_wasm32
      - name: Odin check examples/all for js_wasm64p32
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all -vet -strict-style -disallow-do -no-entry-point -target:js_wasm64p32

      - name: Odin check examples/all/sdl3 for Linux i386
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all/sdl3 -vet -strict-style -disallow-do -no-entry-point -target:linux_i386
      - name: Odin check examples/all/sdl3 for Linux arm64
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all/sdl3 -vet -strict-style -disallow-do -no-entry-point -target:linux_arm64
      - name: Odin check examples/all/sdl3 for FreeBSD amd64
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all/sdl3 -vet -strict-style -disallow-do -no-entry-point -target:freebsd_amd64
      - name: Odin check examples/all/sdl3 for OpenBSD amd64
        if: matrix.os == 'ubuntu-latest'
        run: ./odin check examples/all/sdl3 -vet -strict-style -disallow-do -no-entry-point -target:openbsd_amd64

  build_windows:
    name: Windows Build, Check, and Test
    runs-on: windows-2022
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v4
      - name: build Odin
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          ./build.bat 1
      - name: Odin version
        run: ./odin version
      - name: Odin report
        run: ./odin report
      - name: Odin check
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin check examples/demo -vet
      - name: Odin run
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin run examples/demo
      - name: Odin run -debug
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin run examples/demo -debug -vet -strict-style -disallow-do
      - name: Odin check examples/all
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin check examples/all -vet -strict-style -disallow-do
      - name: Odin check examples/all/sdl3
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin check examples/all/sdl3 -vet -strict-style -disallow-do -no-entry-point
      - name: Core library tests
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin test tests/core/normal.odin -file -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -sanitize:address
      - name: Optimized core library tests
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin test tests/core/speed.odin -o:speed -file -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -sanitize:address
      - name: Vendor library tests
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          copy vendor\lua\5.4\windows\*.dll .
          odin test tests/vendor -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -sanitize:address
      - name: Odin internals tests
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin test tests/internal -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -sanitize:address
      - name: Check issues
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          cd tests/issues
          call run.bat
      - name: Check benchmarks
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin check tests/benchmark -vet -strict-style -no-entry-point
      - name: Odin documentation tests
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          cd tests\documentation
          call build.bat
      - name: Odin check examples/all for Windows 32bits
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin check examples/all -strict-style -target:windows_i386

  build_linux_riscv64:
    runs-on: ubuntu-latest
    name: Linux riscv64 (emulated) Build, Check and Test
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v4

      - name: Download LLVM (Linux)
        run: |
          wget https://apt.llvm.org/llvm.sh
          chmod +x llvm.sh
          sudo ./llvm.sh 18
          echo "/usr/lib/llvm-18/bin" >> $GITHUB_PATH

      - name: Build Odin
        run: ./build_odin.sh release

      - name: Odin version
        run: ./odin version

      - name: Odin report
        run: ./odin report

      - name: Compile needed Vendor
        run: |
          make -C vendor/stb/src
          make -C vendor/cgltf/src
          make -C vendor/miniaudio/src

      - name: Odin check examples/all
        run: ./odin check examples/all -target:linux_riscv64 -vet -strict-style -disallow-do

      - name: Odin check examples/all/sdl3
        run: ./odin check examples/all/sdl3 -target:linux_riscv64 -vet -strict-style -disallow-do -no-entry-point

      - name: Install riscv64 toolchain and qemu
        run: sudo apt-get install -y qemu-user qemu-user-static gcc-12-riscv64-linux-gnu libc6-riscv64-cross

      - name: Odin run
        run: ./odin run examples/demo -vet -strict-style -disallow-do -target:linux_riscv64 -extra-linker-flags:"-fuse-ld=/usr/bin/riscv64-linux-gnu-gcc-12 -static -Wl,-static" -no-rpath

      - name: Odin run -debug
        run: ./odin run examples/demo -debug -vet -strict-style -disallow-do -target:linux_riscv64 -extra-linker-flags:"-fuse-ld=/usr/bin/riscv64-linux-gnu-gcc-12 -static -Wl,-static" -no-rpath

      - name: Normal Core library tests
        run: ./odin test tests/core/normal.odin -file -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -target:linux_riscv64 -extra-linker-flags:"-fuse-ld=/usr/bin/riscv64-linux-gnu-gcc-12 -static -Wl,-static" -no-rpath

      - name: Optimized Core library tests
        run: ./odin test tests/core/speed.odin -o:speed -file -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -target:linux_riscv64 -extra-linker-flags:"-fuse-ld=/usr/bin/riscv64-linux-gnu-gcc-12 -static -Wl,-static" -no-rpath

      - name: Internals tests
        run: ./odin test tests/internal -all-packages -vet -strict-style -disallow-do -define:ODIN_TEST_FANCY=false -define:ODIN_TEST_FAIL_ON_BAD_MEMORY=true -target:linux_riscv64 -extra-linker-flags:"-fuse-ld=/usr/bin/riscv64-linux-gnu-gcc-12 -static -Wl,-static" -no-rpath
