name:  Nightly

on:
  workflow_dispatch:
  schedule:
    - cron: 0 20 * * *

jobs:
  build_windows:
    name: Windows Build
    if: github.repository == 'odin-lang/Odin'
    runs-on: windows-2022
    steps:
      - uses: actions/checkout@v4
      - name: build Odin
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          ./build.bat 1 1
      - name: Odin run
        shell: cmd
        run: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
          odin run examples/demo
      - name: Copy artifacts
        run: |
          rm bin/llvm/windows/LLVM-C.lib
          mkdir dist
          cp odin.exe dist
          cp LICENSE dist
          cp LLVM-C.dll dist
          cp -r shared dist
          cp -r base dist
          cp -r core dist
          cp -r vendor dist
          cp -r bin dist
          cp -r examples dist
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          include-hidden-files: true
          name: windows_artifacts
          path: dist
  build_linux:
    name: Linux Build
    if: github.repository == 'odin-lang/Odin'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: jirutka/setup-alpine@v1
        with:
          branch: edge
      - name: (Linux) Download LLVM
        run: |
          apk add --no-cache \
          musl-dev llvm20-dev clang20 git mold lz4 \
          libxml2-static llvm20-static zlib-static zstd-static \
          make
        shell: alpine.sh --root {0}
      - name: build odin
        # NOTE: this build does slow compile times because of musl
        run: ci/build_linux_static.sh
        shell: alpine.sh {0}
      - name: Odin run
        run: ./odin run examples/demo
      - name: Copy artifacts
        run: |
          FILE="odin-linux-amd64-nightly+$(date -I)"
          mkdir $FILE
          cp odin $FILE
          cp LICENSE $FILE
          cp -r shared $FILE
          cp -r base $FILE
          cp -r core $FILE
          cp -r vendor $FILE
          cp -r examples $FILE
          # Creating a tarball so executable permissions are retained, see https://github.com/actions/upload-artifact/issues/38
          tar -czvf dist.tar.gz $FILE
      - name: Odin run
        run: |
          FILE="odin-linux-amd64-nightly+$(date -I)"
          $FILE/odin run examples/demo
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: linux_artifacts
          path: dist.tar.gz
  build_macos:
    name: MacOS Build
    if: github.repository == 'odin-lang/Odin'
    runs-on: macos-13
    steps:
      - uses: actions/checkout@v4
      - name: Download LLVM and setup PATH
        run: |
          brew update
          brew install llvm@20 dylibbundler lld

      - name: build odin
        # These -L makes the linker prioritize system libraries over LLVM libraries, this is mainly to
        # not link with libunwind bundled with LLVM but link with libunwind on the system.
        run: CXXFLAGS="-L/usr/lib/system -L/usr/lib" make nightly
      - name: Bundle
        run: |
          FILE="odin-macos-amd64-nightly+$(date -I)"
          mkdir $FILE
          cp odin $FILE
          cp LICENSE $FILE
          cp -r shared $FILE
          cp -r base $FILE
          cp -r core $FILE
          cp -r vendor $FILE
          cp -r examples $FILE
          dylibbundler -b -x $FILE/odin -d $FILE/libs -od -p @executable_path/libs
          # Creating a tarball so executable permissions are retained, see https://github.com/actions/upload-artifact/issues/38
          tar -czvf dist.tar.gz $FILE
      - name: Odin run
        run: |
          FILE="odin-macos-amd64-nightly+$(date -I)"
          $FILE/odin run examples/demo
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: macos_artifacts
          path: dist.tar.gz
  build_macos_arm:
    name: MacOS ARM Build
    if: github.repository == 'odin-lang/Odin'
    runs-on: macos-14 # ARM machine
    steps:
      - uses: actions/checkout@v4
      - name: Download LLVM and setup PATH
        run: |
          brew update
          brew install llvm@20 dylibbundler lld

      - name: build odin
        # These -L makes the linker prioritize system libraries over LLVM libraries, this is mainly to
        # not link with libunwind bundled with LLVM but link with libunwind on the system.
        run: CXXFLAGS="-L/usr/lib/system -L/usr/lib" make nightly
      - name: Bundle
        run: |
          FILE="odin-macos-arm64-nightly+$(date -I)"
          mkdir $FILE
          cp odin $FILE
          cp LICENSE $FILE
          cp -r shared $FILE
          cp -r base $FILE
          cp -r core $FILE
          cp -r vendor $FILE
          cp -r examples $FILE
          dylibbundler -b -x $FILE/odin -d $FILE/libs -od -p @executable_path/libs
          # Creating a tarball so executable permissions are retained, see https://github.com/actions/upload-artifact/issues/38
          tar -czvf dist.tar.gz $FILE
      - name: Odin run
        run: |
          FILE="odin-macos-arm64-nightly+$(date -I)"
          $FILE/odin run examples/demo
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: macos_arm_artifacts
          path: dist.tar.gz
  upload_b2:
    runs-on: [ubuntu-latest]
    needs: [build_windows, build_macos, build_macos_arm, build_linux]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.8.x'

      - name: Install B2 SDK
        shell: bash
        run: |
          python -m pip install --upgrade pip
          pip install --upgrade b2sdk

      - name: Display Python version
        run: python -c "import sys; print(sys.version)"

      - name: Download Windows artifacts

        uses: actions/download-artifact@v4.1.7
        with:
          name: windows_artifacts
          path: windows_artifacts

      - name: Download Ubuntu artifacts
        uses: actions/download-artifact@v4.1.7
        with:
          name: linux_artifacts
          path: linux_artifacts

      - name: Download macOS artifacts
        uses: actions/download-artifact@v4.1.7
        with:
          name: macos_artifacts
          path: macos_artifacts

      - name: Download macOS arm artifacts
        uses: actions/download-artifact@v4.1.7
        with:
          name: macos_arm_artifacts
          path: macos_arm_artifacts

      - name: Debug
        run: |
          tree -L 2

      - name: Create archives and upload
        shell: bash
        env:
          APPID: ${{ secrets.B2_APPID }}
          APPKEY: ${{ secrets.B2_APPKEY }}
          BUCKET: ${{ secrets.B2_BUCKET }}
          DAYS_TO_KEEP: ${{ secrets.B2_DAYS_TO_KEEP }}
        run: |
          file linux_artifacts/dist.tar.gz
          python3 ci/nightly.py artifact windows-amd64 windows_artifacts/
          python3 ci/nightly.py artifact linux-amd64 linux_artifacts/dist.tar.gz
          python3 ci/nightly.py artifact macos-amd64 macos_artifacts/dist.tar.gz
          python3 ci/nightly.py artifact macos-arm64 macos_arm_artifacts/dist.tar.gz
          python3 ci/nightly.py prune
          python3 ci/nightly.py json
